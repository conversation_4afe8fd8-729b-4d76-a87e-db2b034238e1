import {
  IGetLoginUrl,
  IGetLoginUrlResponse,
  IHaierIamCodeLogin,
  IHaierIamTokenLogin,
  IUsernameLogin,
  IamLogoutRequest,
  ITravelSessionLogin,
  IUsernameHworkLogin,
  ISupermarketTokenLogin,
  IWyyTokenLogin,
} from '@haierbusiness-front/common-libs';
import { get, post } from '../request';

export const loginApi = {
  /**
   * 获取登录url
   */
  getLoginUrl: (params: IGetLoginUrl): Promise<IGetLoginUrlResponse> => {
    return get('auth/api/support/login/url', params);
  },

  /**
   * 海尔统一code登录
   */
  haierIamCodeLogin: (params: IHaierIamCodeLogin): Promise<string> => {
    return post('auth/api/login/iam_code', {
      code: params.code,
      application_code: params.applicationCode,
    });
  },

  /**
   * 海尔统一token登录
   */
  haierIamTokenLogin: (params: IHaierIamTokenLogin, headers?: any, errFuc?: any): Promise<string> => {
    return post('auth/api/login/iam_token', params, headers || {}, errFuc || null);
  },

  /**
   * 生态session登录
   */
  travelSessionLogin: (params: ITravelSessionLogin): Promise<string> => {
    return post('auth/api/login/travel_session', params);
  },

  /**
   * hwork 用户名工号登录
   */
  hiWorkUser: (params: IUsernameHworkLogin): Promise<string> => {
    return post('auth/api/login/hiWorkUser', params);
  },

  /**
   * 超市token登录
   */
  supermarketTokenLogin: (params: ISupermarketTokenLogin): Promise<string> => {
    return post('auth/api/login/supermarket_token', params);
  },

  /**
   * 网易云token登录
   */
  wyyTokenLogin: (params: IWyyTokenLogin): Promise<string> => {
    return post('auth/api/login/ntes_token', params);
  },

  /**
   * 用户名密码登录
   */
  usernameLogin: (params: IUsernameLogin): Promise<string> => {
    return post('auth/api/login/username', params);
  },

  /**
   * 海尔统一认证登出
   */
  haierIamTokenLogout: (params: IamLogoutRequest): Promise<void> => {
    return post('auth/api/iam/support/logout', params);
  },

  /**
   * 验证海尔token是否过期
   */
  checkIamToken: (params: IamLogoutRequest): Promise<void> => {
    return get('auth/api/iam/support/checkIamToken', params);
  },
};

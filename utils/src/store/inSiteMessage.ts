import { IInSiteMessage } from '@haierbusiness-front/common-libs';
import { inSiteMessageApi } from '@haierbusiness-front/apis';
import { notification } from 'ant-design-vue'
import { defineStore } from 'pinia'

type InSiteMessageState = {
    // 站内信列表
    messageList: Array<IInSiteMessage>
}

// 站内信
export const inSiteMessageStore = defineStore('inSiteMessage', {
    state: (): InSiteMessageState => {
        return {
            messageList: [] 
        }
    },
    actions: {
        // 获取站内信列表并对比本地缓存，如果有新增的，则覆盖本地缓存并弹出通知
        async getMessageList() {
            // 获取站内信列表
            const details = await inSiteMessageApi.oneSelfList({ pageNum: 1, pageSize: 5, isRead: 1 });
            // 对比本地缓存，如果有新增的，则覆盖本地缓存并弹出通知
            const localMessageList: Array<IInSiteMessage> = this.messageList;
            const newMessageList: Array<IInSiteMessage> = details.records || [];
            const diffMessageList: Array<IInSiteMessage> = newMessageList.filter(item => !localMessageList.some(localItem => localItem.id === item.id));
            if (diffMessageList.length > 0) {
                diffMessageList.forEach(item => {
                    notification.info({
                        message: '您有一条新的消息，请及时查看！',
                        description: item.content,
                    });
                });
            }
        },
    }
})
<!-- 满意度评价 -->
<script setup lang="ts">
import {
  Button,
  Col,
  Row,
  Modal,
  Rate,
  Input,
  Upload,
  message
} from 'ant-design-vue';
import { fileApi, miceBidManOrderListApi, satisfactionRatingApi } from '@haierbusiness-front/apis';
import { computed, ref, onMounted, watch } from 'vue';
import satisfiedIcon from '@/assets/image/satisfactionRating/satisfied.png'
import acceptableIcon from '@/assets/image/satisfactionRating/acceptable.png'
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
import { OneEndedMeeting, UploadFiles } from '@haierbusiness-front/common-libs';

interface Props {
  show: boolean;
  list: OneEndedMeeting | null
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

//会议标题
const meetingTitle = ref('')
const meetingId = ref()
//上传图片
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const fileList = ref<UploadFiles[]>([]);
const loading = ref<boolean>(false);

const upload = async (options: any) => {
  loading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  const res = await fileApi.upload(formData);
  const file = {
    ...options.file,
    name: options.file.name,
    url: baseUrl + res.path,
  };
  loading.value = false;
  fileList.value = [...fileList.value, file];
  console.log(fileList.value, "fileList.value");

  options.onProgress(100);
  options.onSuccess(res, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
};

const beforeUpload = (file) => {
  return new Promise((resolve, reject) => {
    // 第一阶段：仅校验可同步获取的属性
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return reject(new Error('Invalid file type'));
    }

    const isLt5M = file.size / 1024 / 1024 < 2;
    if (!isLt5M) {
      message.error('图片大小不能超过2MB!');
      return reject(new Error('File too large'));
    }

    // 第二阶段：异步检测图片实际尺寸
    const reader = new FileReader();
    reader.onload = (e) => {
      const img = new Image();
      img.onload = () => {
        message.success(`图片尺寸: ${img.naturalWidth}x${img.naturalHeight}`);
        resolve(file); // 返回文件对象更符合规范:ml-citation{ref="3" data="citationList"}
      };
      img.onerror = () => reject(new Error('Image load failed'));
      img.src = e.target.result;
    };
    reader.onerror = () => reject(new Error('File read failed'));
    reader.readAsDataURL(file);
  });
};

//登陆人会议列表
const meetingDetails = ref<OneEndedMeeting | null>()

watch(
  () => props.list,
  (newVal, oldVal) => {
    console.log(props.list, "props.list");

    // 处理变化逻辑
    meetingDetails.value = newVal
    // console.log(meetingDetails.value, "meetingDetails.value");
    if (meetingDetails.value) {
      console.log(meetingDetails.value,"meetingDetails.value");
      meetingTitle.value = meetingDetails.value.miceName
      meetingId.value = meetingDetails.value.id
    }
  },
  { deep: true }
)


// 满意度评价功能
const ratingVisible = ref(props.show);
const detailMode = ref(false);
const accommodation = ref(5);
const feedback = ref('');


const emit = defineEmits(["cancel", "handleok"]);

// 处理简单评价点击
const handleRatingClick = (score: number) => {
  if (score === 5) {
    submitRating(score);
  }
};

// 显示详细评分模式
const showDetailMode = () => {
  detailMode.value = true;
};

// 提交评分
const submitRating = (score?: number) => {
  const evaluateScore = score || accommodation.value;
  if(!evaluateScore){
    message.info('请选择评分')
    return
  }
  // 打印传递的参数
  const params = {
    miceId: meetingId.value,
    evaluateState: evaluateScore,
    description: feedback.value,
    path: []
  };
  if (fileList.value.length > 0) {
    params.path = fileList.value.map(item => ({
      type: 2,
      path: item.url
    }));
  }
  console.log('提交参数:', params);

  // 使用Promise处理评价提交
  return new Promise((resolve, reject) => {
    satisfactionRatingApi.save(params as any)
      .then(res => {
        console.log('提交成功，响应:', res);
        emit("handleok")
        detailMode.value = false;
        accommodation.value = 0;
        feedback.value = '';
        fileList.value = []
        resolve(res);
      })
      .catch(error => {
        console.error('提交评价失败:', error);
        reject(error);
      });
  });
};

// 移除文件
const handleRemove = (file: UploadFiles) => {
  const index = fileList.value.indexOf(file);
  if (index !== -1) {
    fileList.value.splice(index, 1);
  }
};

</script>

<template>
  <!-- 满意度评价弹框 -->
  <a-modal v-model:open="ratingVisible" :footer="null" :closable="true" :mask-closable="false"
    :width="detailMode ? 550 : 350" centered @cancel="emit('cancel')">
    <!-- 简单评价模式 -->
    <div v-if="!detailMode" class="rating-simple">
      <div class="rating-title">会议{{ meetingTitle }}已结束，期待您的评价！您的意见对我们非常重要。</div>
      <div class="rating-buttons">
        <div class="rating-button" @click="handleRatingClick(5)">
          <div class="icon-wrapper">
            <img :src="satisfiedIcon" alt="很满意" class="rating-icon" />
          </div>
          <div class="text">很满意</div>
        </div>
        <div class="rating-button" @click="showDetailMode">
          <div class="icon-wrapper">
            <img :src="acceptableIcon" alt="还可以" class="rating-icon" />
          </div>
          <div class="text">还可以</div>
        </div>
      </div>
    </div>

    <!-- 详细评分模式 -->
    <div v-else class="rating-detail">
      <div class="rating-title">会议{{ meetingTitle }}已结束，期待您的评价！您的意见对我们非常重要。</div>
      <div class="rating-buttons">
        <div class="rating-button" @click="handleRatingClick(5)">
          <div class="emoji-wrapper">
            <img :src="satisfiedIcon" alt="">
          </div>
          <div class="text">很满意</div>
        </div>
        <div class="rating-button active">
          <div class="emoji-wrapper">
            <img :src="acceptableIcon" alt="">
          </div>
          <div class="text">还可以</div>
        </div>
      </div>
      <div class="divider"></div>
      <div></div>
      <div class="rating-form">
        <div class="rating-item">
          <div>整体评分：</div>
          <div class="stars">
            <a-rate v-model:value="accommodation" />
            <span class="score">{{ accommodation }}分</span>
          </div>
        </div>
        <div class="feedback-input" v-if="accommodation < 5">
          <Input.TextArea v-model:value="feedback" placeholder="很抱歉数据未达预期，请详细描述您不满意的地方..." :rows="4" allow-clear
            :maxlength="500" showCount />
        </div>
      </div>
      <div class="rating-form flex">
        <div>
          <div style="line-height: 32px;">上传图片：</div>
        </div>
        <div style="display: flex;flex-direction: column;justify-content: left;width: 75%;">
          <a-upload :file-list="fileList" :show-upload-list="true" name="avatar" :custom-request="upload"
            :before-upload="beforeUpload" @remove="handleRemove">
            <div style="display: flex;justify-content: left;">
              <a-button>上传图片</a-button>
            </div>
          </a-upload>
        </div>
      </div>
      <div class="upload-hint" style="text-align: left;">建议上传图片的尺寸为400*400 ，大小不超过2MB ，格式为png/jpg/jpeg的文件。</div>
      <div class="rating-actions">
        <a-button type="primary" @click="submitRating()" :disabled="accommodation < 5 && !feedback">提交</a-button>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

/* 评价弹框样式 */
.rating-simple,
.rating-detail {
  padding: 20px 10px;
  text-align: center;
}

.rating-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 20px;
}

.rating-buttons {
  display: flex;
  justify-content: space-around;
  margin-top: 30px;
}

.rating-button {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;

  &.active {
    .icon-wrapper {
      box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
    }

    .text {
      font-weight: bold;
    }
  }
}

.emoji-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 48px;
  background-color: #f5f5f5;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.rating-button:hover .emoji-wrapper {
  background-color: #e6f7ff;
  border-color: #40a9ff;
}

.rating-button.active .emoji-wrapper {
  background-color: #e6f7ff;
  border-color: #1890ff;
  box-shadow: 0 0 8px rgba(24, 144, 255, 0.3);
}

.text {
  font-size: 14px;
}

.divider {
  border-top: 1px dashed #ddd;
  margin: 20px 0;
  width: 100%;
}

.rating-form {
  margin-top: 10px;
}

.rating-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.label {
  width: 60px;
  text-align: right;
  margin-right: 15px;
}

.stars {
  flex: 1;
  display: flex;
  align-items: center;
}

.score {
  margin-left: 10px;
  color: #ff6b6b;
  font-weight: 500;
}

.feedback-input {
  margin: 20px 0;
}

.rating-actions {
  margin-top: 20px;
  text-align: center;
}

.upload-hint {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

:deep(.ant-upload-wrapper) {
  display: flex;
  flex-direction: column;
  justify-content: left;
}

:deep(.ant-upload-list-item-name) {
  text-align: left;
}
</style>

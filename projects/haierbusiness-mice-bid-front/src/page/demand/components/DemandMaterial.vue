<script setup lang="ts">
// 布展物料
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, defineProps, defineEmits, defineExpose, inject } from 'vue';
import { MaterialTypeConstant, MaterialsArr, materialDetailsArr } from '@haierbusiness-front/common-libs';
import { demandApi } from '@haierbusiness-front/apis';

const props = defineProps({
  cacheStr: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['demandMaterialsFunc']);

// AI测算价格新增数量
const demandAiCalcAddFunc = inject('demandAiCalcAddFunc', () => {});
const demandAiCalcDelFunc = inject('demandAiCalcDelFunc', () => {});

const demandMaterialFormRef = ref();

// 布展物料表单
const formState = reactive<MaterialsArr>({
  // 布展物料
  demandTotalPrice: null, // 总预算
  calcTotalPrice: null, // 自动测算总价

  materialDetails: [], // 需求布展物料明细
});

const tableHead = ['物料类型', '规格说明', '数量', '单位', '单价（元）', '操作'];
// 布展物料
const materialParams = ref<materialDetailsArr>({
  // 需求布展物料明细
  miceDemandMaterialId: null, // 需求布展物料表id
  type: null, // 物料类型
  specs: '', // 规格说明
  num: null, // 数量
  unitPrice: null, // 单价
  unit: null, // 单价

  isType: false, // 物料类型
  isSpecs: false, // 规格说明
  isNum: false, // 数量
  isUnit: false, // 单位
  isUnitPrice: false, // 单价
});

// 校验
const rules = {
  demandTotalPrice: [{ required: true, message: '请填写总预算', trigger: 'change' }],
};

// 锚点 - 用户点击添加需求，需跳转至添加位置
const anchorId = (id: string) => {
  document?.getElementById(id)?.scrollIntoView({
    behavior: 'smooth', //smooth:平滑，auto：直接定位
    block: 'center',
    inline: 'start',
  });
};

// 提交
const onSubmit = async () => {
  let isVerifyPassed = false;

  await demandMaterialFormRef.value
    .validate()
    .then(() => {
      emit('demandMaterialsFunc', { ...formState });

      isVerifyPassed = true;
    })
    .catch((err) => {
      isVerifyPassed = false;

      anchorId('demandMaterialsId');
    });

  return isVerifyPassed;
};

// 暂存
const tempSave = () => {
  emit('demandMaterialsFunc', { ...formState });
};

defineExpose({ onSubmit, tempSave });

// 需求 - 删除
const removeHotel = (item: materialDetailsArr) => {
  if (formState.materialDetails?.length > 1) {
    const index = formState.materialDetails.indexOf(item);
    if (index !== -1) {
      formState.materialDetails.splice(index, 1);
    }

    calcPrice(1);
  }
};
// 需求 - 添加
const addMaterial = () => {
  formState.materialDetails.push({ ...materialParams.value, miceDemandMaterialId: Date.now() });
};

// 价格测算
const calcPrice = async (index: Number) => {
  // if (!formState.demandTotalPrice) {
  //   return;
  // }

  let checkedList = [];
  let priceNum = 0;

  formState.materialDetails.forEach((e) => {
    const isChecked = e.type >= 0 && e.num && e.unitPrice ? true : false;
    checkedList.push(isChecked);

    if (e.num && e.unitPrice) {
      priceNum = priceNum + e.num * e.unitPrice;
    }
  });

  if (index >= 0) {
    formState.demandTotalPrice = priceNum;
  }

  const allChecked = checkedList.every((j) => j === true);

  if (allChecked) {
    const mDetails = formState.materialDetails.map((e) => {
      return {
        // miceDemandMaterialId: parseInt(e.miceDemandMaterialId),
        type: e.type,
        specs: e.specs === '以服务商提报为准' ? null : e.specs,
        num: e.num,
        unitPrice: e.unitPrice,
      };
    });

    const calcParams = {
      demandTotalPrice: formState.demandTotalPrice, // 总预算
      materialDetails: mDetails,
    };

    demandAiCalcAddFunc();

    const res = await demandApi.priceCalcMaterial({
      ...calcParams,
    });

    console.log('%c [ 布展物料-自动测算单价 ]-120', 'font-size:13px; background:pink; color:#bf2c9f;', res);

    formState.calcTotalPrice = res.calcTotalPrice; //

    demandAiCalcDelFunc();

    onSubmit();
  }
};

// 校验
const checkMaterial = function (rule, value, callback, index, obj) {
  const e = formState.materialDetails[index];
  if (value === undefined || value === null || value === '') {
    e[obj] = true;

    return Promise.reject('');
  }

  e[obj] = false;

  return Promise.resolve();
};

onMounted(async () => {
  addMaterial();

  // 反显
  if (props.cacheStr) {
    const cacheObj = JSON.parse(props.cacheStr);
    if (cacheObj.material && Object.keys(cacheObj.material).length > 0) {
      formState.demandTotalPrice = cacheObj.material.demandTotalPrice;
      formState.calcTotalPrice = cacheObj.material.calcTotalPrice;

      formState.materialDetails = [];
      cacheObj.material.materialDetails.forEach((e, i) => {
        formState.materialDetails.push({ ...e, miceDemandMaterialId: Date.now() + i });
      });
    }
  }
});
</script>

<template>
  <!-- 布展物料 -->
  <div class="demand_material demand_pad24">
    <div class="demand_title">
      <div class="demand_border"></div>
      <span>布展物料</span>
    </div>

    <div class="plan_col_list mt20">
      <div class="plan_col_title">布展物料</div>

      <a-form class="mt20" ref="demandMaterialFormRef" :model="formState" :rules="rules" hideRequiredMark>
        <a-row :gutter="12" id="demandMaterialsId">
          <a-col :span="24">
            <div class="demand_hotel">
              <div>
                <div class="hotel_head">
                  <div
                    v-for="(headName, headIndex) in tableHead"
                    :key="headIndex"
                    :class="['table_width' + (headIndex + 1), 'pad012']"
                  >
                    {{ headName }}
                  </div>
                </div>
                <div
                  v-for="(materialItem, index) in formState.materialDetails"
                  :key="materialItem.key"
                  :class="[
                    'hotel_content',
                    materialItem.isType ||
                    materialItem.isSpecs ||
                    materialItem.isNum ||
                    materialItem.isUnit ||
                    materialItem.isUnitPrice
                      ? 'height64'
                      : '',
                  ]"
                >
                  <div class="table_width1">
                    <a-form-item
                      :name="['materialDetails', index, 'type']"
                      :rules="{
                        required: true,
                        message: '请选择物料类型',
                        validator: (rule, value, callback) => checkMaterial(rule, value, callback, index, 'isType'),
                      }"
                    >
                      <a-select
                        v-model:value="materialItem.type"
                        @change="calcPrice(index)"
                        :placeholder="'请选择'"
                        allow-clear
                        :bordered="false"
                        style="width: 100%"
                      >
                        <a-select-option
                          v-for="item in MaterialTypeConstant.toArray()"
                          :key="item.code"
                          :value="item.code"
                        >
                          {{ item.desc }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </div>

                  <div class="table_width2">
                    <a-form-item
                      :name="['materialDetails', index, 'specs']"
                      :rules="{
                        required: true,
                        message: '请填写规格说明',
                        validator: (rule, value, callback) => checkMaterial(rule, value, callback, index, 'isSpecs'),
                      }"
                    >
                      <a-input
                        v-model:value="materialItem.specs"
                        @blur="calcPrice(index)"
                        placeholder="请填写规格说明"
                        :maxlength="500"
                        allow-clear
                        :bordered="false"
                      />
                    </a-form-item>
                  </div>

                  <div class="table_width3">
                    <a-form-item
                      label=""
                      :name="['materialDetails', index, 'num']"
                      :rules="{
                        required: true,
                        message: '请填写数量',
                        validator: (rule, value, callback) => checkMaterial(rule, value, callback, index, 'isNum'),
                      }"
                    >
                      <a-input-number
                        v-model:value="materialItem.num"
                        @blur="calcPrice(index)"
                        placeholder="请填写数量"
                        :bordered="false"
                        allow-clear
                        :min="1"
                        :max="999999"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </div>

                  <div class="table_width4">
                    <a-form-item
                      :name="['materialDetails', index, 'unit']"
                      :rules="{
                        required: true,
                        message: '请填写单位',
                        validator: (rule, value, callback) => checkMaterial(rule, value, callback, index, 'isUnit'),
                      }"
                    >
                      <a-input
                        v-model:value="materialItem.unit"
                        @blur="calcPrice(index)"
                        placeholder="请填写单位"
                        :maxlength="500"
                        allow-clear
                        :bordered="false"
                      />
                    </a-form-item>
                  </div>

                  <div class="table_width5">
                    <a-form-item
                      label=""
                      :name="['materialDetails', index, 'unitPrice']"
                      :rules="{
                        required: true,
                        message: '请填写单价',
                        validator: (rule, value, callback) =>
                          checkMaterial(rule, value, callback, index, 'isUnitPrice'),
                      }"
                    >
                      <a-input-number
                        v-model:value="materialItem.unitPrice"
                        @blur="calcPrice(index)"
                        placeholder="请填写单价"
                        :bordered="false"
                        allow-clear
                        :min="1"
                        :max="999999"
                        :precision="2"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </div>
                  <div class="table_width6 pad012">
                    <div
                      :class="['demand_del', formState.materialDetails.length > 1 ? 'del_hover' : 'del_disabled']"
                      @click="removeHotel(materialItem)"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
            <div class="demand_add mt16 mb42 ml15" @click="addMaterial">
              <div class="demand_add_img mr8"></div>
              <span>添加需求</span>
            </div>
          </a-col>
          <a-col :span="8">
            <a-form-item label="总预算：" required name="demandTotalPrice">
              <a-input-number
                v-model:value="formState.demandTotalPrice"
                @blur="calcPrice(-1)"
                placeholder="请填写总预算"
                addon-after="元"
                disabled
                allow-clear
                :min="1"
                :max="999999"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </div>
</template>

<style scoped lang="less">
.demand_material {
  .plan_col_list {
    padding: 20px 24px 0;
    background: #f6f9fc;
    border-radius: 8px;
    border: 1px solid #e5e6e8;

    position: relative;

    .plan_col_title {
      background: url('@/assets/image/demand/demand_material.png');
      background-repeat: no-repeat;
      background-size: 18px 18px;
      background-position: left center;

      text-indent: 28px;
      font-weight: 500;
      font-size: 18px;
      line-height: 25px;
      color: #1d2129;
    }
  }

  .demand_hotel {
    display: flex;

    .hotel_head,
    .hotel_content {
      display: flex;
      background: #f2f3f5;
      height: 32px;
      line-height: 32px;
      border-bottom: 1px solid #e5e6eb;

      .table_width1,
      .table_width2,
      .table_width3,
      .table_width4,
      .table_width5 {
        width: 124px;
      }
      .table_width2 {
        width: 300px;
      }

      .table_width6 {
        width: 82px;
        display: flex;
        align-items: center;
      }

      :deep(.ant-input-number) {
        line-height: 30px;
      }

      .pad012 {
        padding: 0 12px;
      }

      .demand_del {
        cursor: pointer;
        width: 16px;
        height: 16px;
        background: url('@/assets/image/demand/demand_del.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .del_hover {
        &:hover {
          width: 16px;
          height: 16px;
          background: url('@/assets/image/demand/demand_del_red.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
      }
      .del_disabled {
        cursor: not-allowed;
      }
    }

    .hotel_content {
      height: 40px;
      line-height: 40px;
      background: #fff;

      /* input */
      :deep(.ant-form-item .ant-form-item-control-input) {
        line-height: 39px;
      }
      /* 下拉框 */
      :deep(.ant-select-single:not(.ant-select-customize-input) .ant-select-selector) {
        padding-left: 11px;
        padding-top: 4px;
        height: 39px;
      }

      :deep(.ant-form-item-explain-error) {
        /* 错误提示 */
        padding-left: 12px;
      }
    }

    .height64 {
      height: 64px;
    }
  }

  .demand_add {
    width: 85px;
    display: flex;
    align-items: center;
    cursor: pointer;

    color: #1868db;
    line-height: 20px;

    &:hover {
      text-decoration: underline;
    }

    .demand_add_img {
      width: 16px;
      height: 16px;
      background: url('@/assets/image/demand/demand_add.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>

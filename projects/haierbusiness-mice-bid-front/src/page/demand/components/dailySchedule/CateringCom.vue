<script setup lang="ts">
// 用餐
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, defineProps, defineEmits, defineExpose, inject } from 'vue';
import {
  hotelLevelAllConstant,
  CateringTypeConstant,
  HotelDinnerTypeConstant,
  CateringTimeTypeConstant,
  HaveDrinksTypeConstant,
  DemandSubmitObj,
  StaysArr,
  DemandCalcCateringObj,
} from '@haierbusiness-front/common-libs';
import { demandApi } from '@haierbusiness-front/apis';

const props = defineProps({
  dateIndex: {
    type: Number,
    default: 0,
  },
  meetingPersonTotal: {
    // 会议人数
    type: Number,
    default: 0,
  },
  cateringList: {
    type: Array,
    default: [],
  },
  hotelList: {
    type: Array,
    default: [],
  },
});

const emit = defineEmits(['demandPlanCateringFunc', 'demandPlanRemoveFunc']);

// AI测算价格新增数量
const demandAiCalcAddFunc = inject('demandAiCalcAddFunc', () => {});
const demandAiCalcDelFunc = inject('demandAiCalcDelFunc', () => {});

const demandPlanFormRef = ref();

// 日程安排表单
const formState = reactive<DemandSubmitObj>({
  cateringsList: [], // 用餐
});

const hotelList = ref<Array>([]); // 酒店列表
const hotelChangeObj = ref<DemandCalcCateringObj>({}); // 酒店选择

// 餐饮提供方列表
watch(
  () => props.hotelList,
  (newList) => {
    hotelList.value = newList.filter((e) => e.centerMarker);

    setTimeout(() => {
      // 用餐
      formState.cateringsList.forEach((e) => {
        // 餐厅需求只有一个时，默认选中
        if (hotelList.value?.length === 1) {
          e.tempDemandHotelId = hotelList.value[0].tempDemandHotelId;
          e.level = hotelList.value[0].level;

          hotelChangeObj.value = hotelList.value[0];
        }

        if (e.tempDemandHotelId && e.centerMarker) {
          const checkedHotel = hotelList.value.filter((j) => j.tempDemandHotelId === e.tempDemandHotelId);

          if (checkedHotel.length === 0) {
            e.tempDemandHotelId = null;
            e.level = null;
            hotelChangeObj.value = {};
          }
        }
      });
    }, 0);
  },
  {
    immediate: true,
    deep: true,
  },
);

// 用餐列表
watch(
  () => props.cateringList,
  (newVal) => {
    formState.cateringsList = [...newVal];
  },
  {
    immediate: true,
    deep: true,
  },
);

// 删除
const removeCatering = (type: String, index: number) => {
  emit('demandPlanRemoveFunc', { type: type, delIndex: index, index: props.dateIndex });
};

// 提交
const onSubmit = async () => {
  let isVerifyPassed = false;

  await demandPlanFormRef.value
    .validate()
    .then(() => {
      emit('demandPlanCateringFunc', { list: [...formState.cateringsList], index: props.dateIndex });

      isVerifyPassed = true;
    })
    .catch((err) => {
      isVerifyPassed = false;
    });

  return isVerifyPassed;
};
defineExpose({ onSubmit });

// 餐饮提供方
const changeHotel = (obj: StaysArr, i: number) => {
  // 星级-赋值
  const hotelObj = hotelList.value.filter((e) => e.tempDemandHotelId == obj.tempDemandHotelId && e.centerMarker);
  formState.cateringsList[i].level = hotelObj[0]?.level || null;

  hotelChangeObj.value = hotelObj[0] || {};
  calcPrice(i);
};

const changeCatering = (i: number) => {
  if (formState.cateringsList[i].isInsideHotel === 1) {
    // 酒店提供 - 1
    // 非酒店提供 - 0

    if (hotelList.value?.length === 1) {
      formState.cateringsList[i].tempDemandHotelId = hotelList.value[0].tempDemandHotelId;
      formState.cateringsList[i].level = hotelList.value[0].level;

      hotelChangeObj.value = hotelList.value[0];
    } else {
      formState.cateringsList[i].tempDemandHotelId = null;
      formState.cateringsList[i].level = null;

      hotelChangeObj.value = {};
    }
  }

  formState.cateringsList[i].cateringType = null;

  calcPrice(i);
};

// 价格测算
const calcPrice = async (i: number) => {
  if (formState.cateringsList[i].isInsideHotel !== 1) {
    formState.cateringsList[i].tempDemandHotelId = null;
    formState.cateringsList[i].level = null;
  }

  if (
    (formState.cateringsList[i].isInsideHotel == 0 || formState.cateringsList[i].isInsideHotel == 1) &&
    (formState.cateringsList[i].cateringType == 0 ||
      formState.cateringsList[i].cateringType == 1 ||
      formState.cateringsList[i].cateringType == 2 ||
      formState.cateringsList[i].cateringType == 3 ||
      formState.cateringsList[i].cateringType == 4) &&
    (formState.cateringsList[i].cateringTime == 0 || formState.cateringsList[i].cateringTime == 1) &&
    formState.cateringsList[i].personNum &&
    formState.cateringsList[i].demandUnitPrice
  ) {
    if (hotelChangeObj.value && Object.keys(hotelChangeObj.value).length === 0) {
      hotelChangeObj.value = hotelList.value[0];
    }

    const calcParams = {
      calcDate: formState.cateringsList[i].demandDate + ' 00:00:00', // 需求日期
      level: formState.cateringsList[i].level, //
      isInsideHotel: formState.cateringsList[i].isInsideHotel, // 是否酒店提供用餐
      cateringType: formState.cateringsList[i].cateringType, // 用餐类型
      cateringTime: formState.cateringsList[i].cateringTime, // 用餐时间

      personNum: formState.cateringsList[i].personNum, // 人数
      demandUnitPrice: formState.cateringsList[i].demandUnitPrice, // 用餐标准
      isIncludeDrinks: formState.cateringsList[i].isIncludeDrinks, // 	是否包含酒水

      cityId: hotelChangeObj.value?.cityId || null, // 酒店所在城市id
      districtIds: hotelChangeObj.value?.districtIds || '', // 酒店所在区域id,支持多区域,逗号分割\n 如果多个区域,则多个区域同时测算,并返回最高的区域的价格
      latitude: hotelChangeObj.value?.latitude || '', // 需求中心点经度
      longitude: hotelChangeObj.value?.longitude || '', // 需求中心点纬度
      distanceRange: hotelChangeObj.value?.distanceRange || null, // 需求范围:单位米(可选)
      centerMarker: hotelChangeObj.value?.centerMarker || '', //	需求中心的地标名称
    };

    demandAiCalcAddFunc();

    const res = await demandApi.priceCalcCatering({
      ...calcParams,
    });

    console.log('%c [ 用餐-自动测算单价 ]-120', 'font-size:13px; background:pink; color:#bf2c9f;', res);

    formState.cateringsList[i].calcUnitPrice = res.calcUnitPrice; //

    demandAiCalcDelFunc();
  }
};

onMounted(async () => {});
</script>

<template>
  <!-- 用餐 -->
  <div class="catering_com">
    <a-form ref="demandPlanFormRef" :model="formState" :labelCol="{ style: { width: '84px' } }" hideRequiredMark>
      <div
        class="plan_col_list mb20"
        v-for="(cateringsItem, cateringsIndex) in formState.cateringsList"
        :key="cateringsIndex"
      >
        <div class="plan_col_title">
          {{ '用餐' + (cateringsIndex + 1) }}
        </div>
        <div class="plan_col_del" @click="removeCatering('catering', cateringsIndex)"></div>

        <a-row :gutter="12" class="mt20">
          <a-col :span="8">
            <a-form-item
              label="餐饮提供方："
              :name="['cateringsList', cateringsIndex, 'isInsideHotel']"
              :rules="{
                required: true,
                message: '请选择餐饮提供方',
                trigger: 'change',
              }"
            >
              <a-select
                v-model:value="cateringsItem.isInsideHotel"
                @change="changeCatering(cateringsIndex)"
                placeholder="请选择餐饮提供方"
                allow-clear
              >
                <a-select-option v-for="item in HotelDinnerTypeConstant.toArray()" :key="item.code" :value="item.code">
                  {{ item.desc }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8" v-if="cateringsItem.isInsideHotel === 1">
            <a-row>
              <a-col :span="cateringsItem.level ? 17 : 24">
                <a-form-item
                  label="酒店位置："
                  :name="['cateringsList', cateringsIndex, 'tempDemandHotelId']"
                  :rules="{
                    required: true,
                    message: '请选择餐饮提供方',
                    trigger: 'change',
                  }"
                  tooltip="酒店位置来自酒店需求"
                >
                  <a-select
                    v-model:value="cateringsItem.tempDemandHotelId"
                    @change="changeHotel(cateringsItem, cateringsIndex)"
                    :disabled="hotelList && hotelList.length === 1"
                    placeholder="请选择餐饮提供方"
                    allow-clear
                  >
                    <a-select-option
                      v-for="(item, idx) in hotelList"
                      :key="item.tempDemandHotelId"
                      :value="item.tempDemandHotelId"
                    >
                      <a-tooltip placement="topLeft" :title="'酒店' + (idx + 1) + '-' + item.centerMarker">
                        {{ '酒店' + (idx + 1) + '-' + item.centerMarker }}
                      </a-tooltip>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show="cateringsItem.level" :span="7">
                <a-form-item label="">
                  <a-select v-model:value="cateringsItem.level" placeholder="请选择酒店星级" disabled>
                    <a-select-option
                      v-for="item in hotelLevelAllConstant.toArray()"
                      :key="item.code"
                      :value="item.code"
                    >
                      {{ item.desc }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="用餐类型："
              :name="['cateringsList', cateringsIndex, 'cateringType']"
              :rules="{
                required: true,
                message: '请选择用餐类型',
                trigger: 'change',
              }"
            >
              <!--
                  勾选酒店提供时，用餐类型为桌餐、自助、盒饭。
                  勾选非酒店提供时，外出用餐、外卖
              -->
              <a-select
                v-model:value="cateringsItem.cateringType"
                @change="calcPrice(cateringsIndex)"
                placeholder="请选择用餐类型"
                allow-clear
              >
                <a-select-option
                  v-for="item in cateringsItem.isInsideHotel === 1
                    ? CateringTypeConstant.toArray().slice(0, 3)
                    : CateringTypeConstant.toArray().slice(3, 5)"
                  :key="item.code"
                  :value="item.code"
                >
                  {{ item.desc }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="用餐时间："
              :name="['cateringsList', cateringsIndex, 'cateringTime']"
              :rules="{
                required: true,
                message: '请选择用餐时间',
                trigger: 'change',
              }"
            >
              <a-select
                v-model:value="cateringsItem.cateringTime"
                @change="calcPrice(cateringsIndex)"
                placeholder="请选择用餐时间"
                allow-clear
              >
                <a-select-option v-for="item in CateringTimeTypeConstant.toArray()" :key="item.code" :value="item.code">
                  {{ item.desc }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="用餐人数："
              :name="['cateringsList', cateringsIndex, 'personNum']"
              :rules="{
                required: true,
                message: '请填写用餐人数',
                trigger: 'change',
              }"
            >
              <a-input-number
                v-model:value="cateringsItem.personNum"
                @blur="calcPrice(cateringsIndex)"
                placeholder="请填写用餐人数"
                allow-clear
                :min="1"
                :max="props.meetingPersonTotal || 99999"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="餐标："
              :name="['cateringsList', cateringsIndex, 'demandUnitPrice']"
              :rules="{
                required: true,
                message: '请填写餐标',
                trigger: 'change',
              }"
            >
              <a-input-number
                v-model:value="cateringsItem.demandUnitPrice"
                @blur="calcPrice(cateringsIndex)"
                placeholder="请填写餐标"
                addon-after="元/位"
                allow-clear
                :min="1"
                :max="999999"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item
              label="是否含酒水："
              :name="['cateringsList', cateringsIndex, 'isIncludeDrinks']"
              :rules="{
                required: true,
                message: '请选择是否含酒水',
                trigger: 'change',
              }"
            >
              <a-radio-group v-model:value="cateringsItem.isIncludeDrinks" @change="calcPrice(cateringsIndex)">
                <a-radio v-for="item in HaveDrinksTypeConstant.toArray()" :key="item.code" :value="item.code">
                  {{ item.desc }}
                </a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </div>
</template>

<style scoped lang="less">
.catering_com {
  .plan_col_list {
    padding: 20px 24px;
    background: #fff;
    border-radius: 8px;

    position: relative;

    .plan_col_title {
      background: url('@/assets/image/demand/demand_catering.png');
      background-repeat: no-repeat;
      background-size: 18px 18px;
      background-position: left center;

      text-indent: 28px;
      font-weight: 500;
      font-size: 18px;
      line-height: 25px;
      color: #1d2129;
    }

    .plan_col_del {
      position: absolute;
      top: 12px;
      right: 12px;

      width: 18px;
      height: 18px;
      background: url('@/assets/image/demand/demand_del_x.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      cursor: pointer;
    }
  }
}
</style>

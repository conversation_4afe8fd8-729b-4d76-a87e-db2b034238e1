<script setup lang="ts">
// 服务人员
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, defineProps, defineEmits, defineExpose, inject } from 'vue';
import { AttendantTypeConstant, AttendantsArr } from '@haierbusiness-front/common-libs';
import { demandApi } from '@haierbusiness-front/apis';

const props = defineProps({
  dateIndex: {
    type: Number,
    default: 0,
  },
  attendantList: {
    type: Array,
    default: [],
  },
  hotelList: {
    type: Array,
    default: [],
  },
});

const emit = defineEmits(['demandPlanAttendantFunc', 'demandPlanRemoveFunc']);

// AI测算价格新增数量
const demandAiCalcAddFunc = inject('demandAiCalcAddFunc', () => {});
const demandAiCalcDelFunc = inject('demandAiCalcDelFunc', () => {});

const demandPlanFormRef = ref();

// 日程安排表单
const formState = reactive<AttendantsArr>({
  attendants: [], // 服务人员
});

// 服务人员列表
watch(
  () => props.attendantList,
  (newVal) => {
    formState.attendants = [...newVal];
  },
  {
    immediate: true,
    deep: true,
  },
);

// 删除
const removeAttendant = (type: String, index: Number) => {
  emit('demandPlanRemoveFunc', { type: type, delIndex: index, index: props.dateIndex });
};

// 提交
const onSubmit = async () => {
  let isVerifyPassed = false;

  await demandPlanFormRef.value
    .validate()
    .then(() => {
      emit('demandPlanAttendantFunc', { list: [...formState.attendants], index: props.dateIndex });
      isVerifyPassed = true;
    })
    .catch((err) => {
      isVerifyPassed = false;
    });

  return isVerifyPassed;
};
defineExpose({ onSubmit });

// 价格测算
const calcPrice = async (i: Number) => {
  if (formState.attendants[i].type >= 0 && formState.attendants[i].personNum) {
    const cityNames = props.hotelList.map((e) => {
      return e.cityName;
    });

    const calcParams = {
      cityName: cityNames.join(','),
      calcDate: formState.attendants[i].demandDate + ' 00:00:00', // 需求日期
      type: formState.attendants[i].type, // 人员类型
      personNum: formState.attendants[i].personNum, // 人数

      duty: formState.attendants[i].duty, // 	工作范围
    };

    demandAiCalcAddFunc();

    const res = await demandApi.priceCalcAttendant({
      ...calcParams,
    });

    console.log('%c [ 服务人员-自动测算单价 ]-120', 'font-size:13px; background:pink; color:#bf2c9f;', res);

    formState.attendants[i].calcUnitPrice = res.calcUnitPrice; //

    demandAiCalcDelFunc();
  }
};

onMounted(async () => {});
</script>

<template>
  <!-- 服务人员 -->
  <div class="attendant_com">
    <a-form ref="demandPlanFormRef" :model="formState" :labelCol="{ style: { width: '84px' } }" hideRequiredMark>
      <div
        class="plan_col_list mb20"
        v-for="(attendantsItem, attendantsIndex) in formState.attendants"
        :key="attendantsIndex"
      >
        <div class="plan_col_title">
          {{ '服务人员' + (attendantsIndex + 1) }}
        </div>
        <div class="plan_col_del" @click="removeAttendant('attendant', attendantsIndex)"></div>

        <a-row :gutter="12" class="mt20">
          <a-col :span="8">
            <a-form-item
              label="人员类型："
              :name="['attendants', attendantsIndex, 'type']"
              :rules="{
                required: true,
                message: '请选择人员类型',
                trigger: 'change',
              }"
            >
              <a-select
                v-model:value="attendantsItem.type"
                @change="calcPrice(attendantsIndex)"
                placeholder="请选择人员类型"
                allow-clear
              >
                <a-select-option v-for="item in AttendantTypeConstant.toArray()" :key="item.code" :value="item.code">
                  {{ item.desc }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="人数："
              :name="['attendants', attendantsIndex, 'personNum']"
              :rules="{
                required: true,
                message: '请填写人数',
                trigger: 'change',
              }"
            >
              <a-input-number
                v-model:value="attendantsItem.personNum"
                @blur="calcPrice(attendantsIndex)"
                placeholder="请填写人数"
                allow-clear
                :min="1"
                :max="999999"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <a-col :span="16">
            <a-form-item
              label="工作范围："
              :name="['attendants', attendantsIndex, 'duty']"
              :rules="{
                required: true,
                message: '请填写工作范围',
                trigger: 'change',
              }"
            >
              <a-textarea
                v-model:value="attendantsItem.duty"
                placeholder="请填写工作范围"
                :autoSize="{ minRows: 3, maxRows: 3 }"
                allow-clear
                :maxlength="500"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </div>
</template>

<style scoped lang="less">
.attendant_com {
  .plan_col_list {
    padding: 20px 24px;
    background: #fff;
    border-radius: 8px;

    position: relative;

    .plan_col_title {
      background: url('@/assets/image/demand/demand_attendant.png');
      background-repeat: no-repeat;
      background-size: 18px 18px;
      background-position: left center;

      text-indent: 28px;
      font-weight: 500;
      font-size: 18px;
      line-height: 25px;
      color: #1d2129;
    }

    .plan_col_del {
      position: absolute;
      top: 12px;
      right: 12px;

      width: 18px;
      height: 18px;
      background: url('@/assets/image/demand/demand_del_x.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      cursor: pointer;
    }
  }
}
</style>

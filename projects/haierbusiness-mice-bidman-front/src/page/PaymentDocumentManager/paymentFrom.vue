<!-- 付款单 -->
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Upload as hUpload,
  Modal,
  message,
  TableProps,
  Table as ATable,
  Tabs as ATabs,
  TabPane as ATabPane,
  Textarea as ATextarea,
  Tooltip,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { paymentFromApi, fileApi } from '@haierbusiness-front/apis';
import { IPaymentFromFilter, IPaymentFrom } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted, nextTick } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables';
import router from '../../router';
import {
  InvoiceStatusEnum,
  InvoiceStatusMap,
  InvoiceStatusTagColorMap,
} from '@haierbusiness-front/common-libs';
import Actions from '@haierbusiness-front/components/actions/Actions.vue';
import type { MenuItemType, MenuInfo } from 'ant-design-vue/lib/menu/src/interface';
// const router = useRouter()

const currentRouter = ref();

onMounted(async () => {
  currentRouter.value = await router;
  // 页面初始化时调用列表接口
  listApiRun({
    pageNum: 1,
    pageSize: 10,
  });
});

const columns: ColumnType[] = [
  {
    title: '付款单号',
    dataIndex: 'paymentCode',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '服务商名称',
    dataIndex: 'merchantName',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '账单总金额',
    dataIndex: 'totalAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '结算比例',
    dataIndex: 'settlementRate',
    width: '80px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '付款金额',
    dataIndex: 'paymentAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center',
  },
];
const searchParam = ref<IPaymentFromFilter>({});
const { data, run: listApiRun, loading, current, pageSize } = usePagination(paymentFromApi.getPage);

const reset = () => {
  searchParam.value = {};
  beginAndEnd.value = undefined;
};

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total || 0,
  current: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

// 删除
const { handleDelete } = useDelete(paymentFromApi, () =>
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 10,
  }),
);

const detailVisible = ref(false);
const currentDetailRecord = ref<any>(null);
const detailLoading = ref(false);
const viewMode = ref<'view' | 'upload'>('view'); // 弹窗模式：查看或上传

//查看
const handleView = (record?: any) => {
  if (record && record.id) {
    // 有传入记录，获取详情数据
    detailLoading.value = true;
    viewMode.value = 'view';
    
    // 调用详情接口获取完整数据
    paymentFromApi.getDetails(record.id).then((res) => {
      currentDetailRecord.value = res;
      detailVisible.value = true;
    }).catch((error) => {
      console.error('获取详情失败:', error);
      message.error('获取详情失败，请重试');
    }).finally(() => {
      detailLoading.value = false;
    });
  } else {
    // 关闭详情弹窗
    detailVisible.value = false;
    currentDetailRecord.value = null;
    fileList.value = [];
    ReasonsRejection.value = '';
  }
};

const beginAndEnd = ref<[Dayjs, Dayjs]>();
watch(
  () => beginAndEnd.value,
  (n: any, o: any) => {
    if (n) {
      searchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00');
      searchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59');
    } else {
      searchParam.value.startTime = undefined;
      searchParam.value.endTime = undefined;
    }
  },
);

// 上传付款凭证相关
const uploadLoading = ref(false);
const activeKey = ref('1');
const fileList = ref<any[]>([]);
const ReasonsRejection = ref('');
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 打开上传付款凭证弹窗
const openUploadModal = (record: any) => {
  detailLoading.value = true;
  viewMode.value = 'upload';
  fileList.value = [];
  ReasonsRejection.value = '';
  
  // 调用详情接口获取完整数据
  paymentFromApi.getDetails(record.id).then((res) => {
    currentDetailRecord.value = res;
    detailVisible.value = true;
  }).catch((error) => {
    console.error('获取详情失败:', error);
    message.error('获取详情失败，请重试');
  }).finally(() => {
    detailLoading.value = false;
  });
};

// 关闭上传弹窗（已合并到handleView函数中）

// 关闭生成付款单弹窗
const closePaymentOrderModal = () => {
  PaymentOrderVisible.value = false;
  settlementList.value = undefined;
  selectedRowKeys.value = []; // 重置选中状态
  // 重置弹窗中的查询条件
  beginAndEnd.value = undefined;
};
//发票
const invoiceColumns: ColumnType<DataType>[] = [
  {
    title: '发票号',
    dataIndex: 'name',
  },
  {
    title: '发票日期',
    dataIndex: 'age',
  },
  {
    title: '发票金额',
    dataIndex: '',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 详情页订单表格列
const detailOrderColumns: ColumnType<DataType>[] = [
  {
    title: '会议单号',
    dataIndex: 'mainCode',
    width: '150px',
    align: 'center',
  },
  {
    title: '会议时间',
    width: '200px',
    align: 'center',
    customRender: ({ record }) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`;
      }
      return '';
    },
  },
  {
    title: '会议负责人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
  },
  {
    title: '账单金额',
    dataIndex: 'billAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '付款比例',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '付款金额',
    dataIndex: 'paymentAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 详情页发票表格列
const detailInvoiceColumns: ColumnType<DataType>[] = [
  {
    title: '发票号',
    dataIndex: 'invoiceNumber',
    align: 'center',
  },
  {
    title: '发票日期',
    dataIndex: 'invoiceDate',
    align: 'center',
  },
  {
    title: '发票金额',
    dataIndex: 'invoiceAmount',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 计算发票金额合计
const calculateInvoiceTotal = () => {
  if (!currentDetailRecord.value) return '0';
  const invoiceData = getInvoiceData();
  const total = invoiceData.reduce((sum: number, item: any) => sum + (item.invoiceAmount || 0), 0);
  return `${total}元`;
};

// 获取发票数据
const getInvoiceData = () => {
  if (!currentDetailRecord.value || !currentDetailRecord.value.miceInvoiceQueryDetails) {
    return [];
  }
  // 使用miceInvoiceQueryDetails作为发票数据源
  return currentDetailRecord.value.miceInvoiceQueryDetails.map((invoice: any, index: number) => ({
    key: invoice.id || index,
    invoiceNumber: invoice.invoiceNumber || '',
    invoiceDate: invoice.invoiceDate || '',
    invoiceAmount: invoice.invoiceAmount || 0,
  }));
};

//上传付款单
const PaymentOrderVisible = ref(false);
//选择的结算单
const settlementList = ref();
// 选中的行键
const selectedRowKeys = ref<Key[]>([]);

const {
  data: PaymentOrderData,
  run: PaymentOrderlist,
  loading: paymentOrderLoading,
  current: paymentOrderCurrent,
  pageSize: paymentOrderPageSize,
} = usePagination(paymentFromApi.getBillList);

const handlePaymentOrder = () => {
  PaymentOrderlist({
    startTime: searchParam.value.startTime,
    endTime: searchParam.value.endTime,
    pageNum: 1,
    pageSize: 10,
  });
};

// PaymentOrder表格分页
const paymentOrderPagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: PaymentOrderData.value?.total || 0,
  current: PaymentOrderData.value?.pageNum || 1,
  pageSize: PaymentOrderData.value?.pageSize || 10,
  style: { justifyContent: 'center' },
}));

// 处理PaymentOrder表格分页变化
const handlePaymentOrderTableChange = (pag: any, filters?: any, sorter?: any) => {
  PaymentOrderlist({
    startTime: searchParam.value.startTime,
    endTime: searchParam.value.endTime,
    pageNum: pag.current || 1,
    pageSize: pag.pageSize || 10,
  });
};
const rowSelection: TableProps['rowSelection'] = {
  selectedRowKeys: selectedRowKeys,
  onChange: (selectedKeys: Key[], selectedRows: DataType[]) => {
    selectedRowKeys.value = selectedKeys;
    settlementList.value = selectedRows;
  },
};
//订单
const PaymentOrderColumns: ColumnType<DataType>[] = [
  {
    title: '会议单号',
    dataIndex: 'mainCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '会议时间',
    dataIndex: 'meetingTime',
    width: '200px',
    align: 'center',
    customRender: ({ record }) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`;
      }
      return '';
    },
  },
  {
    title: '会议负责人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '结算金额',
    dataIndex: 'amount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '付款比例',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '付款金额',
    dataIndex: 'receiveAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];
// 文件上传处理
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  console.log('开始上传文件:', options.file.name);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.fileName = options.file.name;
      options.onProgress(100);
      options.onSuccess(it, options.file);

      console.log('文件上传成功:', options.file);

      // 确保文件被添加到列表中
      if (!fileList.value.some((f) => f.fileName === options.file.name)) {
        fileList.value.push(options.file);
      }
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('文件上传失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 文件删除处理
const handleFileRemove = (file: any) => {
  const index = fileList.value.findIndex((f) => f.uid === file.uid);
  if (index > -1) {
    fileList.value.splice(index, 1);
  }
};

// 提交上传的付款凭证
const submitUpload = () => {
  if (fileList.value.length === 0) {
    message.error('请先上传付款凭证');
    return;
  }

  if (!currentDetailRecord.value?.id) {
    message.error('记录ID不存在');
    return;
  }

  // 提取文件路径
  const attachmentFiles = fileList.value.map((file) => file.filePath).filter(Boolean) as string[];

  if (attachmentFiles.length === 0) {
    message.error('文件上传未完成，请重试');
    return;
  }

  uploadLoading.value = true;

  // 调用confirmPayment接口
  paymentFromApi
    .confirmPayment({
      id: currentDetailRecord.value.id,
      attachmentFile: attachmentFiles,
    })
    .then(() => {
      message.success('付款凭证上传成功');
      handleView(); // 关闭弹窗
      // 刷新列表
      listApiRun({
        ...searchParam.value,
        pageNum: data.value?.pageNum || 1,
        pageSize: data.value?.pageSize || 10,
      });
    })
    .catch((error) => {
      console.error('提交付款凭证失败:', error);
      message.error('提交失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 提交生成付款单
const submitPaymentOrder = () => {
  if (!settlementList.value || settlementList.value.length === 0) {
    message.error('请先选择会议');
    return;
  }

  // 提取选中会议的结算单ID列表
  const balanceIds = settlementList.value.map((item: any) => item.id).filter(Boolean);

  if (balanceIds.length === 0) {
    message.error('选中的会议中没有有效的结算单ID');
    return;
  }

  // 调用生成付款单接口
  paymentFromApi
    .create({
      balanceIds: balanceIds,
    })
    .then(() => {
      message.success('付款单生成成功');
      closePaymentOrderModal();
      // 刷新列表
      listApiRun({
        ...searchParam.value,
        pageNum: data.value?.pageNum || 1,
        pageSize: data.value?.pageSize || 10,
      });
    })
    .catch((error) => {
      console.error('生成付款单失败:', error);
      message.error('生成付款单失败，请重试');
    });
};

// 处理菜单点击事件
const handleMenuClick = (record: any, e: MenuInfo) => {
  const key = e.key as string;
  switch (key) {
    case 'delete':
      handleDelete(record.id);
      break;
    case 'upload':
      openUploadModal(record);
      break;
    default:
      break;
  }
};

// 计算菜单选项
const getMenuOptions = (record: any) => {
  const options: MenuItemType[] = [];

  // 根据状态添加不同的操作选项
  if (record.status === InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD) {
    // 待服务商上传发票：显示删除按钮
    options.push({
      key: 'delete',
      label: '删除',
    });
  } else if (record.status == InvoiceStatusEnum.PENDING_FINANCIAL_CONFIRM) {
    // 待财务确认收款：显示上传付款凭证按钮
    options.push({
      key: 'upload',
      label: '上传付款凭证',
    });
  }
  // 已完成状态只显示查看，不需要额外的菜单选项

  return options;
};

// 生成付款单
const generatePaymentOrder = () => {
  PaymentOrderVisible.value = true;
  // 打开弹窗时就调用接口获取数据
  PaymentOrderlist({
    startTime: searchParam.value.startTime,
    endTime: searchParam.value.endTime,
    pageNum: 1,
    pageSize: 10,
  });
};
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="serviceProvider">服务商：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.merchantCode" placeholder="请输入服务商" allow-clear />
          </h-col>

          <h-col :span="3" style="text-align: right; padding-right: 10px">
            <label for="createTime">付款单创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="beginAndEnd" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="status">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.status" placeholder="请选择状态" allow-clear style="width: 100%">
              <h-select-option :value="InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD">
                {{ InvoiceStatusMap[InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD] }}
              </h-select-option>
              <h-select-option :value="InvoiceStatusEnum.PENDING_FINANCIAL_CONFIRM">
                {{ InvoiceStatusMap[InvoiceStatusEnum.PENDING_FINANCIAL_CONFIRM] }}
              </h-select-option>
              <h-select-option :value="InvoiceStatusEnum.FINANCIAL_REJECTED">
                {{ InvoiceStatusMap[InvoiceStatusEnum.FINANCIAL_REJECTED] }}
              </h-select-option>
              <h-select-option :value="InvoiceStatusEnum.COMPLETED">
                {{ InvoiceStatusMap[InvoiceStatusEnum.COMPLETED] }}
              </h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="12" style="text-align: left">
            <h-button type="primary" @click="generatePaymentOrder">
              <PlusOutlined />
              生成付款单
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :scroll="{ x: 1400 }"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'paymentCode'">
              <Tooltip :title="record.paymentCode">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">{{ record.paymentCode }}</div>
              </Tooltip>
            </template>
            <template v-if="column.dataIndex === 'merchantName'">
              <Tooltip :title="record.merchantName">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">{{ record.merchantName }}</div>
              </Tooltip>
            </template>
            <template v-if="column.dataIndex === 'status'">
              {{ InvoiceStatusMap[record.status as keyof typeof InvoiceStatusMap] || '未知状态' }}
            </template>
                        <template v-if="column.dataIndex === '_operator'">
              <div class="operator-buttons">
                <h-button type="link" @click="handleView(record)">查看</h-button>
                <Actions
                  v-if="getMenuOptions(record).length > 0"
                  :menu-options="getMenuOptions(record)"
                  :on-menu-click="(e) => handleMenuClick(record, e)"
                >
                </Actions>
              </div>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    <!-- 生成付款单弹窗 -->
    <Modal
      v-model:open="PaymentOrderVisible"
      title="生成付款单"
      :footer="null"
      @cancel="closePaymentOrderModal"
      width="60%"
    >
      <div>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="4" style="text-align: right; padding-right: 10px">
            <label for="createTime">会议创建时间：</label>
          </h-col>
          <h-col :span="6">
            <h-range-picker v-model:value="beginAndEnd" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="4" style="text-align: right">
            <h-button type="primary" @click="handlePaymentOrder()">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <a-table
          :row-key="(record) => record.id"
          :row-selection="rowSelection"
          :columns="PaymentOrderColumns"
          :data-source="PaymentOrderData?.records || []"
          :loading="paymentOrderLoading"
          :pagination="paymentOrderPagination"
          @change="handlePaymentOrderTableChange"
          :scroll="{ x: 1000 }"
          style="margin-top: 15px;width: 100%;"
        >
          <template #bodyCell="{ column, text }"> </template>
        </a-table>
        <div style="text-align: right; margin-top: 20px">
          <h-button style="margin-right: 10px" @click="closePaymentOrderModal">取消</h-button>
          <h-button type="primary" @click="submitPaymentOrder" :loading="loading">生成付款单</h-button>
        </div>
      </div>
    </Modal>

    <!-- 删除了独立的上传付款凭证弹窗，已合并到详情弹窗中 -->

    <!-- 付款单详情/上传付款凭证弹窗 -->
    <Modal
      v-model:open="detailVisible"
      :title="viewMode === 'view' ? '付款单详情' : '上传付款凭证'"
      :footer="null"
      @cancel="handleView()"
      width="800px"
      :loading="detailLoading"
    >
      <div v-if="currentDetailRecord" style="padding: 20px 0">
        <!-- 基本信息 -->
        <div style="margin-bottom: 20px">
          <div style="margin-bottom: 12px"><strong>付款单号：</strong>{{ currentDetailRecord.paymentCode }}</div>
          <div style="margin-bottom: 12px"><strong>服务商名称：</strong>{{ currentDetailRecord.merchantName }}</div>
          <div style="margin-bottom: 12px"><strong>付款总金额：</strong>{{ currentDetailRecord.totalAmount }}元</div>
          <!-- 查看模式：显示已上传的付款凭证 -->
          <div v-if="viewMode === 'view'" style="margin-bottom: 12px">
            <strong>付款凭证：</strong>
            <template v-if="currentDetailRecord.attachmentFiles && currentDetailRecord.attachmentFiles.length > 0">
              <template v-for="(file, index) in currentDetailRecord.attachmentFiles" :key="index">
                <a :href="file.path" target="_blank" style="margin-right: 10px; color: #1890ff">
                  {{ file.path ? file.path.split('/').pop() || `付款凭证${index + 1}` : `付款凭证${index + 1}` }}
                </a>
              </template>
            </template>
            <span v-else>无</span>
          </div>
        </div>

        <!-- Tab页 -->
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane key="1" tab="订单">
            <h-table
              :columns="detailOrderColumns"
              :data-source="currentDetailRecord.paymentRecordsDetails || []"
              :pagination="false"
              size="small"
              bordered
              style="margin-top: 10px"
            >
            </h-table>
          </a-tab-pane>
          <a-tab-pane key="2" tab="发票">
            <div style="margin-bottom: 16px"><strong>发票金额合计：</strong>{{ calculateInvoiceTotal() }}</div>
            <h-table
              :columns="detailInvoiceColumns"
              :data-source="getInvoiceData()"
              :pagination="false"
              size="small"
              bordered
              style="margin-top: 10px"
            >
            </h-table>
          </a-tab-pane>
        </a-tabs>

        <!-- 上传模式：显示上传区域和驳回原因 -->
        <div v-if="viewMode === 'upload'" style="margin-bottom: 20px">
          <div style="margin-bottom: 16px">
            <label style="font-weight: bold">付款凭证：</label>
            <h-upload
              v-model:fileList="fileList"
              :custom-request="uploadRequest"
              :multiple="true"
              :max-count="5"
              @remove="handleFileRemove"
              accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
              :show-upload-list="true"
            >
              <h-button :loading="uploadLoading">
                <UploadOutlined />
                上传文件
              </h-button>
            </h-upload>
          </div>
          <div style="margin-bottom: 16px">
            <label style="font-weight: bold">驳回原因：</label>
            <a-textarea
              v-model:value="ReasonsRejection"
              show-count
              :maxlength="200"
              placeholder="请填写驳回原因（驳回时必填）"
              style="margin-top: 8px"
            />
          </div>
        </div>
        <!-- 底部按钮 -->
        <div style="text-align: right; margin-top: 20px">
          <h-button style="margin-right: 10px" @click="handleView()">取消</h-button>
          <!-- 查看模式：只显示确定按钮 -->
          <h-button v-if="viewMode === 'view'" type="primary" @click="handleView()">确定</h-button>
          <!-- 上传模式：显示确定按钮 -->
          <h-button v-if="viewMode === 'upload'" type="primary" @click="submitUpload" :loading="uploadLoading"
            >确定
          </h-button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style scoped lang="less">
.main-container {
  background-color: #ffff;
  height: 100%;
  width: 100%;
  padding: 10px 10px 0px 10px;
  overflow: auto;
}

.margin-bottom-10 {
  margin-bottom: 10px;
}

.padding-standard {
  padding: 10px 10px 0px 10px;
}

.text-right-padding {
  text-align: right;
  padding-right: 10px;
}

.width-full {
  width: 100%;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.margin-right-10 {
  margin-right: 10px;
}

.modal-padding {
  padding: 20px 0;
}

.info-item {
  margin-bottom: 16px;
}

.info-item-12 {
  margin-bottom: 12px;
}

.upload-section {
  margin: 16px 0;
  display: flex;
}

.textarea-section {
  margin-bottom: 16px;
  display: flex;
}

.footer-buttons {
  text-align: right;
  margin-top: 20px;
}

.table-margin {
  margin-top: 15px;
}

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.operator-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.operator-buttons :deep(.ant-btn) {
  padding: 0 4px;
  font-size: 14px;
}
:deep(table) {
  table-layout: auto !important; /* 恢复默认自适应布局 */
}
</style>

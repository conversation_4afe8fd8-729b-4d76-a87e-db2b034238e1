<!-- 流程编排新增 -->
<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Row as hRow,
  Col as hCol,
  DatePicker as hDatePicker,
  message,
  Upload as hUpload,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  InputNumber as hInputNumber,
  Button as hButton,
  Switch as hSwitch,
  Card as hCard,
  Drawer as hDrawer,
  Table as hTable,
  Empty as hEmpty,
  Tooltip as hTooltip,
  Pagination as hPagination,
} from 'ant-design-vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';
import { computed, ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import type { Ref } from 'vue';
import {
  IProcessOrchestration,
  IRuleMetadata,
  IRoleRule,
  IUserListRequest,
  ProcessOrchestrationServiceTypeEnum,
  MerchantTypeMap,
  MerchantTypeEnum,
} from '@haierbusiness-front/common-libs';

// 导入应用存储，获取当前登录用户信息
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';

interface IExtendedProcessOrchestration extends IProcessOrchestration {
  isHotelDemandSubmittable?: boolean;
  supportInternational?: boolean;
  processNotice?: string;
  platformFeeRate?: number; // 平台服务费费率
}

// 扩展 Window 接口以支持节点缓存数据
declare global {
  interface Window {
    processNodesData?: any;
    updateProcessNodeData?: (data: any) => void;
    manualSaveProcessData?: () => Promise<void>;
    deleteCacheData?: () => Promise<void>;
    clearCacheAfterSubmit?: () => Promise<void>;
  }
}
import { serviceProviderApi, processOrchestrationApi, departmentApi } from '@haierbusiness-front/apis';
import router from '../../router';
import UserSelectPerson from '@haierbusiness-front/components/user/UserSelectPerson.vue';

// 获取登录用户信息
const { loginUser } = storeToRefs(applicationStore());

import Editor from '@haierbusiness-front/components/editor/Editor.vue';

// 导入缓存相关工具方法
import { saveDataBy, getDataBy, delData } from '@haierbusiness-front/utils';

// const router = useRouter()
const route = useRoute();

const currentRouter = ref();

const from = ref();
const confirmLoading = ref(false);
const id = ref<number>();

// 添加查看模式状态
const isViewMode = ref(false);

// 导入功能相关状态
const importModalVisible = ref(false);
const importData = ref('');
const importLoading = ref(false);

// 暂存功能相关状态
const autoSaveTimer = ref<NodeJS.Timeout | null>(null);
const countdownTimer = ref<NodeJS.Timeout | null>(null);
const countdownTime = ref(60);
const cacheRestoreModalVisible = ref(false);
const cacheData = ref<any>(null);
const cacheKey = ref<string>(''); // 缓存key，在组件初始化时生成

// 定义需要使用UserSelect的规则类型
const userSelectRuleTypes = ref<string[]>(['集团内部门', '集团内用户均可', '指定集团内员工工号']);

// 用于UserSelect组件的参数
const userSelectParams = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});

// 部门列表数据
const deptList = ref<any[]>([]);

// 服务商列表数据
const serviceProviderList = ref<any[]>([]);

// 集团外企业列表数据
const enterpriseList = ref<any[]>([]);

// 集团外企业搜索关键词
const enterpriseKeyword = ref<string>('');

// 获取部门列表
const fetchDept = async (value: string) => {
  try {
    // 这里简化处理，直接使用空字符串作为企业代码
    const enterpriseCode = '';

    // 修改这里，将value作为部门名称参数传入
    const res = await departmentApi.departmentList(enterpriseCode, value, 1, 19999999);

    if (res && res.records) {
      deptList.value = res.records;
    }
  } catch (error) {
    console.error('获取部门列表失败:', error);
  }
};

const fetchServiceProvider = async (value: string) => {
  try {
    const searchValue = value || '';
    const res = await serviceProviderApi.listProcessing({ name: searchValue, pageNum: 1, pageSize: 19999999 });

    if (res && res.records) {
      serviceProviderList.value = res.records.filter((item) => item && item.code && item.name);
    } else {
      serviceProviderList.value = [];
    }
  } catch (error) {
    message.error('获取服务商列表失败');
    serviceProviderList.value = [];
  }
};

// 获取集团外企业列表
const fetchEnterprise = async (keyword: string = '') => {
  try {
    const params: any = {};
    if (keyword) {
      params.name = keyword;
    }
    const res = await processOrchestrationApi.listEnterprise(params);

    if (res && res.records) {
      enterpriseList.value = res.records;
    }
  } catch (error) {
    console.error('获取集团外企业列表失败:', error);
  }
};

// 搜索集团外企业
const searchEnterprise = (value: string) => {
  enterpriseKeyword.value = value;
  fetchEnterprise(value);
};

// 修改用户选择列表类型定义，添加 enterpriseCode 属性
const selectedUsers = ref<
  Array<{
    type: string;
    user: string | string[] | any[] | Record<string, any> | any;
    enterpriseCode?: string;
    enterpriseData?: { code: string; name: string }[];
    deptData?: { code: string; name: string }[];
  }>
>([]);

// 添加一个用户选择
const addUserSelection = () => {
  selectedUsers.value.push({
    type: '',
    user: [],
    enterpriseCode: '',
  });
};

// 移除一个用户选择
const removeUserSelection = (index: number) => {
  // 确保至少保留一个用户选择行
  if (selectedUsers.value.length > 1) {
    selectedUsers.value.splice(index, 1);
  } else {
    message.warning('至少需要保留一个适用用户');
  }
};

// 判断当前类型是否应该使用UserSelect组件
const shouldUseUserSelect = (type: string) => {
  // 如果没有类型选择，则不使用UserSelect
  if (!type) return false;
  // 查找对应的规则元数据
  const ruleMetadata = roleRuleOptions.value.find((rule) => rule.ruleMetaKey === type);
  if (!ruleMetadata) return false;

  // 检查规则名称是否包含特定关键词
  return (
    ruleMetadata.ruleMetaName.includes('集团内') &&
    (ruleMetadata.ruleMetaName.includes('部门') ||
      ruleMetadata.ruleMetaName.includes('用户') ||
      ruleMetadata.ruleMetaName.includes('工号'))
  );
};

// 用于获取用户名列表的计算属性
const getUsernames = (user: any[], index: number) => {
  console.log(user, 'user');
  if (!Array.isArray(user)) return [];

  // 获取当前选择的类型
  const currentType = selectedUsers.value[index]?.type;

  // 根据类型处理
  switch (currentType) {
    // 指定集团内员工工号
    case 'roleUsernameIncludeCondition':
      return user.map((u) => {
        return {
          username: u?.username || '',
          nickName: u?.nickName || u?.username || '',
          id: u?.id || u?.username || '',
        };
      });

    // 其他类型返回原始数据
    default:
      return user.map((u) => {
        if (typeof u === 'string') {
          return {
            username: u,
            nickName: u,
            id: u,
          };
        }
        return {
          username: u?.username || '',
          nickName: u?.nickName || '',
          id: u?.id || u?.id || '',
        };
      });
  }
};

// 处理用户选择变更
const handleUserChange = (value: any, index: number) => {
  console.log('handleUserChange触发，详细value:', value);

  // 处理清空的情况（value为null、undefined或空数组）
  if (!value || (Array.isArray(value) && value.length === 0)) {
    selectedUsers.value[index].user = [];
    // 确保触发界面更新
    selectedUsers.value = [...selectedUsers.value];
    return;
  }

  // 获取选择的类型
  const selectedType = selectedUsers.value[index].type;

  try {
    // 根据不同类型处理
    switch (selectedType) {
      // 指定集团内员工工号
      case 'roleUsernameIncludeCondition':
        if (Array.isArray(value) && value.length > 0) {
          if (typeof value[0] === 'object' && value[0].username) {
            // 保留原始格式，因为UserSelectPerson组件需要这些信息
            selectedUsers.value[index].user = value;
          } else {
            // 处理"名称（工号）"格式
            selectedUsers.value[index].user = value.map((item) => {
              const match = typeof item === 'string' && item.match(/(.+)（(.+)）/);
              if (match) {
                const [_, nickName, username] = match;
                return {
                  username,
                  nickName,
                  id: username,
                };
              }
              return {
                username: String(item),
                nickName: String(item),
                id: String(item),
              };
            });
          }
        } else if (value && typeof value === 'object' && value.username) {
          selectedUsers.value[index].user = [value];
        }
        break;

      // 集团外企业
      case 'roleExternalEnterpriseCondition':
        // 对于集团外企业，只需要企业代码
        selectedUsers.value[index].user = Array.isArray(value) ? value : [value];
        // 存储企业编码和企业名称数据，便于后续构建reverseDisplayParam
        selectedUsers.value[index].enterpriseData = Array.isArray(value)
          ? value.map((code) => {
              const enterprise = enterpriseList.value.find((e) => e.code === code);
              return enterprise ? { code, name: enterprise.name } : { code, name: code };
            })
          : [];
        break;

      // 集团内部门
      case 'roleDepartIncludeCondition':
        // 对于集团内部门，只需要部门编码
        selectedUsers.value[index].user = Array.isArray(value) ? value : [value];
        // 存储部门编码和部门名称数据，便于后续构建reverseDisplayParam
        selectedUsers.value[index].deptData = Array.isArray(value)
          ? value.map((code) => {
              const dept = deptList.value.find((d) => d.code?.toString() === code);
              return dept ? { code, name: dept.name } : { code, name: code };
            })
          : [];
        break;

      // 集团内用户均可
      case 'roleUsernameAllCondition':
        // 这个类型不需要选择具体用户
        selectedUsers.value[index].user = [];
        break;

      // 默认处理其他类型
      default:
        if (Array.isArray(value)) {
          selectedUsers.value[index].user = value;
        } else if (value) {
          selectedUsers.value[index].user = [value];
        } else {
          selectedUsers.value[index].user = [];
        }
    }

    // 触发更新
    selectedUsers.value = [...selectedUsers.value];
  } catch (e) {
    console.error('处理用户选择出错:', e);
    selectedUsers.value[index].user = Array.isArray(value) ? [...value] : [value];
    selectedUsers.value = [...selectedUsers.value];
  }
};

// 匹配规则选项
const matchRuleOptions = ref<Array<IRuleMetadata> | any>([]);
// 角色规则选项
const roleRuleOptions = ref<IRoleRule[]>([]);

// 匹配值选项，根据规则类型动态获取
const matchValueOptions = ref<Record<string, Array<{ value: string; label: string }>>>({});

// 存储规则配置数据
const ruleConfigData = ref<Record<string, any>>({});

// 获取匹配规则列表
const getMatchRuleList = async () => {
  try {
    const res = await processOrchestrationApi.listMatchRule({});
    if (res) {
      matchRuleOptions.value = res;

      // 预处理所有CHECKBOX类型的选项
      res.forEach((rule: any) => {
        if (rule.configs && rule.configs.type === 'CHECKBOX' && rule.configs.value) {
          matchValueOptions.value[rule.configs.key] = rule.configs.value;
        }
      });
    } else {
      message.warning('未获取到匹配规则数据');
    }
  } catch (error) {
    console.error('获取匹配规则失败:', error);
  }
};

// 监听匹配规则变化
const handleRuleChange = async (ruleKey: any, cardIndex: number, poolIndex: number) => {
  // 当规则变化时，立即清空当前匹配值
  resourcePoolCards.value[cardIndex].resourcePools[poolIndex].matchValue = [];
  resourcePoolCards.value[cardIndex].resourcePools[poolIndex].selectedCheckboxValues = [];

  // 查找选中的规则配置
  if (ruleKey) {
    const selectedRule = matchRuleOptions.value.find((item: any) => item.configs && item.configs.key === ruleKey);
    if (selectedRule && selectedRule.configs) {
      // 直接设置类型和描述
      resourcePoolCards.value[cardIndex].resourcePools[poolIndex].matchValueType = selectedRule.configs.type;
      resourcePoolCards.value[cardIndex].resourcePools[poolIndex].matchDescription = selectedRule.configs.description;
      resourcePoolCards.value[cardIndex].resourcePools[poolIndex].matchKey = selectedRule.configs.key;

      // 如果是服务商相关的规则，预先获取服务商数据
      if (ruleKey.includes('SELECT') || selectedRule.configs.description?.includes('服务商')) {
        await fetchServiceProvider('');
      }

      // 如果是CHECKBOX类型，设置选项
      if (selectedRule.configs.type === 'CHECKBOX' && selectedRule.configs.value) {
        matchValueOptions.value[ruleKey] = selectedRule.configs.value;
      }
    }
  }
};

// 监听适用用户类型变化
const handleUserTypeChange = (ruleKey: string, index: number) => {
  selectedUsers.value[index].user = [];
};

// 初始化资源池数据
const initResourcePools = () => {
  // 清空硬编码的资源池数据
  resourcePools.value = [
    {
      matchRule: '',
      matchValue: '',
      matchValueType: '',
      matchDescription: '',
      matchKey: '',
      selectedCheckboxValues: [],
    },
  ];

  // 初始化资源池卡片
  resourcePoolCards.value = [
    {
      id: 1,
      name: '', // 资源池名称 - 移除默认值
      resourcePools: [
        {
          matchRule: '',
          matchValue: '',
          matchValueType: '',
          matchDescription: '',
          matchKey: '',
          selectedCheckboxValues: [],
        },
      ],
      businessTypes: [],
      serviceFee: '0',
      businessTypesSecond: [],
      applicationGroups: [''], // 移除默认值
      payee: '', // 收款方
      paymentRate: 0, // 付款比例
      schemeAutoCommit: false, // 是否方案自动提报 - 改为布尔类型
      bidAutoPush: false, // 是否自动竞价 - 改为布尔类型
      fullServiceRemark: '', // 全单服务费说明
      isBasePriceMerchant: false, // 是否底价服务商
    },
  ];

  // 确保资源组初始有一个空值
  if (resourceGroups.value.length === 0) {
    resourceGroups.value = [
      {
        name: '', // 移除默认值
        configStatus: {},
      },
    ];
  }
};

// 获取角色规则列表
const getRoleRuleList = async () => {
  try {
    const res = await processOrchestrationApi.listRoleRule({});
    if (res) {
      roleRuleOptions.value = (Array.isArray(res as any) ? (res as any) : []) as IRoleRule[];
      updateUserSelectConfig();
    } else {
      message.warning('未获取到角色规则数据');
    }
  } catch (error) {
    console.error('获取角色规则失败:', error);
  }
};

// 更新用户选择相关配置
const updateUserSelectConfig = () => {
  const userSelectTypes: string[] = [];
  roleRuleOptions.value.forEach((option) => {
    if (
      option.ruleMetaName.includes('集团内') &&
      (option.ruleMetaName.includes('部门') ||
        option.ruleMetaName.includes('用户') ||
        option.ruleMetaName.includes('工号'))
    ) {
      userSelectTypes.push(option.ruleMetaKey);
    }
  });

  userSelectRuleTypes.value = userSelectTypes;
};

onMounted(async () => {
  currentRouter.value = await router;
  const currentId = currentRouter.value.currentRoute.query?.id;
  const viewMode = currentRouter.value.currentRoute.query?.viewMode;
  id.value = Number(currentId);

  // 检查是否为查看模式
  isViewMode.value = viewMode === 'true';

  // 初始化缓存key
  initCacheKey();

  // 获取匹配规则列表
  await getMatchRuleList();

  // 获取角色规则列表
  await getRoleRuleList();

  // 初始化资源池数据
  initResourcePools();

  // 获取部门列表
  fetchDept('');

  // 获取集团外企业列表
  fetchEnterprise('');

  // 获取服务商列表
  fetchServiceProvider('');

  if (id.value) {
    await get(id.value);
  } else {
    processOrchestration.value = {};
    // 初始化新增模式的默认值
    processOrchestration.value.isHotelDemandSubmittable = false;
    processOrchestration.value.supportInternational = false;
    processOrchestration.value.platformFeeRate = 0;
    // 新增模式下检查是否有缓存数据
    await checkCacheData();
  }

  // 默认添加一个空的用户选择
  if (selectedUsers.value.length === 0) {
    addUserSelection();
  }
  startAutoSave()
});

// 组件销毁前清理定时器
onBeforeUnmount(() => {
  stopAutoSave();
});

watch(
  () => currentRouter.value?.currentRoute.query,
  (newValue) => {
    id.value = Number(newValue.id);
    if (id.value) {
      get(id.value);
    } else {
      processOrchestration.value = {
        isHotelDemandSubmittable: false,
        supportInternational: false,
        platformFeeRate: 0,
      };
    }
  },
);

const get = async (id: number) => {
  const data = await processOrchestrationApi.get(id) as any;
  if (data && data.id) {
    processOrchestration.value = data;

    // 确保修改内容字段能够正确回显
    if ((data as any).changeContent && !processOrchestration.value.modifyContent) {
      processOrchestration.value.modifyContent = (data as any).changeContent;
    }

    // 确保多酒店需求开关字段能够正确回显
    if (data.isHotelDemandSubmittable !== undefined) {
      processOrchestration.value.isHotelDemandSubmittable = data.isHotelDemandSubmittable;
    } else {
      processOrchestration.value.isHotelDemandSubmittable = false;
    }

    // 确保国际会议提报开关字段能够正确回显
    if (data.supportInternational !== undefined) {
      processOrchestration.value.supportInternational = data.supportInternational;
    } else {
      processOrchestration.value.supportInternational = false;
    }

    // 处理 nodes 数据，按 seq 排序并设置默认展开
    if (Array.isArray((data as any).nodes) && (data as any).nodes.length > 0) {
      // 按照 seq 属性排序
      (data as any).nodes.sort((a: any, b: any) => {
        return (a.seq || 0) - (b.seq || 0);
      });

      // 设置所有节点默认展开
      (data as any).nodes.forEach((node: any) => {
        node.isExpanded = true;
      });
    }

    if (Array.isArray((data as any).consumers) && (data as any).consumers.length > 0) {
      selectedUsers.value = (data as any).consumers.map((consumer: any) => {
        let userValue: any = [];
        try {
          // 根据不同的规则类型进行差异化处理
          switch (consumer.ruleMetaKey) {
            // 集团外企业类型
            case 'roleExternalEnterpriseCondition':
              try {
                // 解析企业编号数组 - ruleMetaParamJson是逗号分隔的字符串
                let enterpriseCodes: string[] = [];
                if (consumer.ruleMetaParamJson) {
                  if (consumer.ruleMetaParamJson.startsWith('[')) {
                    // 如果是JSON数组格式
                    enterpriseCodes = JSON.parse(consumer.ruleMetaParamJson);
                  } else {
                    // 如果是逗号分隔的字符串
                    enterpriseCodes = consumer.ruleMetaParamJson
                      .split(',')
                      .map((code: string) => code.trim())
                      .filter(Boolean);
                  }
                }

                // 解析reverseDisplayParam获取企业数据
                let enterpriseData: any[] = [];
                if (consumer.reverseDisplayParam) {
                  try {
                    const parsedReverseValue = JSON.parse(consumer.reverseDisplayParam);
                    if (Array.isArray(parsedReverseValue)) {
                      enterpriseData = parsedReverseValue;
                    }
                  } catch (e) {
                    console.error('解析reverseDisplayParam失败:', e);
                  }
                }

                // 设置用户值为企业编码数组
                userValue = enterpriseCodes;

                // 保存企业数据用于回显
                return {
                  type: consumer.ruleMetaKey || '',
                  user: enterpriseCodes,
                  enterpriseData: enterpriseData,
                };
              } catch (e) {
                console.error('解析集团外企业数据失败:', e);
                userValue = [];
              }
              break;

            // 集团内部门类型
            case 'roleDepartIncludeCondition':
              try {
                // 解析部门编号数组 - ruleMetaParamJson是逗号分隔的字符串
                let departmentCodes: string[] = [];
                if (consumer.ruleMetaParamJson) {
                  if (consumer.ruleMetaParamJson.startsWith('[')) {
                    // 如果是JSON数组格式
                    departmentCodes = JSON.parse(consumer.ruleMetaParamJson);
                  } else {
                    // 如果是逗号分隔的字符串
                    departmentCodes = consumer.ruleMetaParamJson
                      .split(',')
                      .map((code: string) => code.trim())
                      .filter(Boolean);
                  }
                }

                // 解析reverseDisplayParam获取部门数据
                let deptData: any[] = [];
                if (consumer.reverseDisplayParam) {
                  try {
                    const parsedReverseValue = JSON.parse(consumer.reverseDisplayParam);
                    if (Array.isArray(parsedReverseValue)) {
                      deptData = parsedReverseValue;
                    }
                  } catch (e) {
                    console.error('解析部门reverseDisplayParam失败:', e);
                  }
                }

                // 设置用户值为部门编码数组
                userValue = departmentCodes;

                // 保存部门数据用于回显
                return {
                  type: consumer.ruleMetaKey || '',
                  user: departmentCodes,
                  deptData: deptData,
                };
              } catch (e) {
                console.error('解析集团内部门数据失败:', e);
                userValue = [];
              }
              break;

            // 指定集团内员工工号类型
            case 'roleUsernameIncludeCondition':
              try {
                // 如果有reverseDisplayParam，优先使用它来回显
                if (consumer.reverseDisplayParam) {
                  try {
                    const parsedReverseValue = JSON.parse(consumer.reverseDisplayParam);
                    if (Array.isArray(parsedReverseValue)) {
                      // 需要包含username和nickName用于展示用户名和工号
                      userValue = parsedReverseValue;
                    }
                  } catch (e) {
                    console.error('解析用户reverseDisplayParam失败:', e);
                  }
                } else {
                  // 解析工号数组
                  const usernames = JSON.parse(consumer.ruleMetaParamJson || '[]');
                  // 构建基本对象，仅包含工号
                  userValue = usernames.map((username: string) => ({
                    username,
                    nickName: username, // 没有nickName时使用username
                    id: username,
                  }));
                }
              } catch (e) {
                console.error('解析指定集团内员工工号数据失败:', e);
                userValue = [];
              }
              break;

            // 集团内用户均可类型
            case 'roleUsernameAllCondition':
              // 这种类型不需要选择具体用户
              userValue = [];
              break;

            // 其他类型
            default:
              // 原有逻辑处理其他类型
              try {
                // 优先使用reverseDisplayParam进行回显
                if (consumer.reverseDisplayParam) {
                  try {
                    const parsedReverseValue = JSON.parse(consumer.reverseDisplayParam || '[]');
                    if (Array.isArray(parsedReverseValue)) {
                      userValue = parsedReverseValue;
                    } else {
                      userValue = consumer.reverseDisplayParam;
                    }
                  } catch (e) {
                    userValue = consumer.reverseDisplayParam;
                  }
                } else if (typeof consumer.ruleMetaParamJson === 'string') {
                  try {
                    const parsedValue = JSON.parse(consumer.ruleMetaParamJson);
                    if (Array.isArray(parsedValue)) {
                      userValue = parsedValue.map((username: string) => ({
                        username,
                        nickName: username,
                        id: username,
                      }));
                    } else {
                      userValue = [
                        {
                          username: String(consumer.ruleMetaParamJson),
                          nickName: String(consumer.ruleMetaParamJson),
                          id: String(consumer.ruleMetaParamJson),
                        },
                      ];
                    }
                  } catch (e) {
                    userValue = [
                      {
                        username: String(consumer.ruleMetaParamJson),
                        nickName: String(consumer.ruleMetaParamJson),
                        id: String(consumer.ruleMetaParamJson),
                      },
                    ];
                  }
                } else {
                  userValue = String(consumer.ruleMetaParamJson);
                }
              } catch (e) {
                // 解析失败，当作普通字符串处理
                userValue = [
                  {
                    username: String(consumer.ruleMetaParamJson),
                    nickName: String(consumer.ruleMetaParamJson),
                    id: String(consumer.ruleMetaParamJson),
                  },
                ];
              }
          }
        } catch (e) {
          console.error('处理回显数据失败:', e);
          // 解析失败，当作普通字符串处理
          userValue = [
            {
              username: String(consumer.ruleMetaParamJson),
              nickName: String(consumer.ruleMetaParamJson),
              id: String(consumer.ruleMetaParamJson),
            },
          ];
        }

        return {
          type: consumer.ruleMetaKey || '',
          user: userValue,
        };
      });
    }

    // 处理业务类型bitmap转换为字符串数组
    if (typeof data.items === 'number') {
      const itemsBitmap = data.items;
      const itemsArray: string[] = [];

      // 遍历映射关系，检查位运算结果
      Object.entries(ProcessOrchestrationServiceTypeEnum).forEach(([key, value]) => {
        // 排除方法属性和非数字值
        if (typeof value === 'number') {
          if ((itemsBitmap & value) === value) {
            const typeText = ProcessOrchestrationServiceTypeEnum.getTypeText(value);
            if (typeText !== '未知') {
              itemsArray.push(typeText);
            }
          }
        }
      });

      processOrchestration.value.items = itemsArray;
    }

    // 处理全局平台服务费配置
    if (data.platformFeeRate !== undefined) {
      processOrchestration.value.platformFeeRate = data.platformFeeRate;
    }

    // 处理资源组数据
    if (Array.isArray((data as any).merchantGroups) && (data as any).merchantGroups.length > 0) {
      resourceGroups.value = (data as any).merchantGroups.map((group: any) => {
        return {
          name: group.name || '',
          serviceFeeRate: 0, // 不再从资源组中读取费率
          businessTypes: [], // 不再从资源组中读取业务类型
          configStatus: {},
        };
      });
    }

    // 处理资源池数据
    if (Array.isArray((data as any).merchantPools) && (data as any).merchantPools.length > 0) {
      // 首先清空现有资源池卡片
      resourcePoolCards.value = [];

      // 遍历资源池数据创建资源池卡片
      (data as any).merchantPools.forEach((pool: any, index: number) => {
        // 构建匹配规则数组
        const resourcePoolsData =
          Array.isArray(pool.matches) && pool.matches.length > 0
            ? pool.matches.map((match: any) => {
                let matchValue = match.ruleMetaParamJson || '';
                let selectedCheckboxValues: string[] = [];
                let matchValueType = '';
                let matchDescription = '';
                let matchKey = '';

                // 查找该规则的配置信息
                const ruleConfig = matchRuleOptions.value.find(
                  (rule: any) => rule.configs && rule.configs.key === match.ruleMetaKey,
                );

                if (ruleConfig && ruleConfig.configs) {
                  matchValueType = ruleConfig.configs.type || '';
                  matchDescription = ruleConfig.configs.description || '';
                  matchKey = ruleConfig.configs.key || '';
                }

                // 处理特殊类型的值
                if (match.ruleMetaKey === 'poolNameExcludeCondition') {
                  // 排除指定服务商 - 处理JSON数组格式
                  try {
                    const parsedValue = JSON.parse(matchValue);
                    if (Array.isArray(parsedValue)) {
                      matchValue = parsedValue;
                    }
                  } catch (e) {
                    // 如果解析失败，尝试按逗号分隔的字符串处理
                    matchValue = matchValue
                      .split(',')
                      .map((v: string) => v.trim())
                      .filter(Boolean);
                  }
                  matchValueType = 'SELECT'; // 强制指定为SELECT类型
                } else if (match.ruleMetaKey === 'poolNameIncludeCondition') {
                  // 选中指定服务商 - 处理逗号分隔的字符串
                  try {
                    // 尝试解析JSON
                    const parsedValue = JSON.parse(matchValue);
                    if (Array.isArray(parsedValue)) {
                      matchValue = parsedValue;
                    } else {
                      // 如果不是数组，按逗号分隔
                      matchValue = matchValue
                        .split(',')
                        .map((v: string) => v.trim())
                        .filter(Boolean);
                    }
                  } catch (e) {
                    // 如果解析失败，按逗号分隔字符串处理
                    matchValue = matchValue
                      .split(',')
                      .map((v: string) => v.trim())
                      .filter(Boolean);
                  }
                  matchValueType = 'SELECT'; // 强制指定为SELECT类型
                } else if (match.ruleMetaKey === 'poolTypeCondition') {
                  // 按服务商类型匹配 - 处理CHECKBOX类型
                  try {
                    // 保存原始的选中值数组
                    selectedCheckboxValues = matchValue
                      .split(',')
                      .map((v) => v.trim())
                      .filter(Boolean);

                    // 将值转换为对应的文本标签，用于显示
                    const labelText = getCheckboxLabelsFromValues(match.ruleMetaKey, selectedCheckboxValues);
                    if (labelText) {
                      matchValue = labelText;
                    }
                  } catch (e) {
                    selectedCheckboxValues = [];
                  }
                  matchValueType = 'CHECKBOX'; // 强制指定为CHECKBOX类型
                } else if (
                  match.ruleMetaKey &&
                  (match.ruleMetaKey.includes('SELECT') || match.ruleMetaKey.includes('服务商'))
                ) {
                  // 其他服务商相关规则
                  try {
                    const parsedValue = parseServiceProviderValue(matchValue);
                    matchValue = parsedValue;
                    matchValueType = 'SELECT'; // 设置为SELECT类型
                  } catch (e) {
                    console.error('解析服务商数据失败:', e);
                  }
                } else if (match.ruleMetaParamJson) {
                  // 其他规则，如果有值，尝试解析为选中值数组
                  selectedCheckboxValues = match.ruleMetaParamJson
                    .split(',')
                    .map((v: string) => v.trim())
                    .filter(Boolean);
                }

                // 如果有反向显示参数，优先使用来设置matchValue
                if (match.reverseDisplayParam) {
                  try {
                    const reverseData = JSON.parse(match.reverseDisplayParam);
                    if (Array.isArray(reverseData) && reverseData.length > 0) {
                      // 对于排除和包含服务商的规则，提取编码作为匹配值
                      if (
                        match.ruleMetaKey === 'poolNameExcludeCondition' ||
                        match.ruleMetaKey === 'poolNameIncludeCondition'
                      ) {
                        matchValue = reverseData.map((item: any) => item.code);
                      }
                    }
                  } catch (e) {
                    console.error('解析reverseDisplayParam失败:', e);
                  }
                }

                return {
                  matchRule: match.ruleMetaKey || '',
                  matchValue: matchValue,
                  matchValueType: matchValueType,
                  matchDescription: matchDescription,
                  matchKey: matchKey,
                  selectedCheckboxValues: selectedCheckboxValues,
                };
              })
            : [
                {
                  matchRule: '',
                  matchValue: '',
                  matchValueType: '',
                  matchDescription: '',
                  matchKey: '',
                  selectedCheckboxValues: [],
                },
              ];

        // 提取业务类型列表
        const businessTypes = Array.isArray(pool.itemRanges)
          ? pool.itemRanges
              .map((range: any) => {
                // 根据itemType数字获取对应的业务类型文本
                return ProcessOrchestrationServiceTypeEnum.getTypeText(range.itemType);
              })
              .filter(Boolean)
          : [];

        // 获取服务费率（现在从外层获取）
        const serviceFee = String(pool.fullServiceRangeRateLimit || 0);

        // 查找此资源池关联的资源组
        const relatedGroups = (data as any).merchantGroups
          ? (data as any).merchantGroups
              .filter((group: any) => Array.isArray(group.poolIds) && group.poolIds.includes(pool.id))
              .map((group: any) => group.name)
          : [''];

        // 根据 fullServiceRange 反推哪些业务类型被选中了作为第二个业务类型
        const businessTypesSecond: string[] = [];
        if (pool.fullServiceRange) {
          // 遍历所有可能的业务类型，检查哪些在 fullServiceRange 中
          Object.entries(ProcessOrchestrationServiceTypeEnum).forEach(([key, value]) => {
            if (typeof value === 'number' && value !== 0) {
              // 使用位运算检查该业务类型是否在 fullServiceRange 中
              if ((pool.fullServiceRange & value) === value) {
                const typeText = ProcessOrchestrationServiceTypeEnum.getTypeText(value);
                if (typeText && typeText !== '未知') {
                  businessTypesSecond.push(typeText);
                }
              }
            }
          });
        }

        // 添加资源池卡片
        resourcePoolCards.value.push({
          id: pool.id || index + 1,
          name: pool.name || (pool.id ? `资源池${index + 1}` : ''), // 编辑模式保持兼容性，新增模式无默认值
          resourcePools: resourcePoolsData,
          businessTypes: businessTypes,
          serviceFee: serviceFee,
          businessTypesSecond: businessTypesSecond.length > 0 ? businessTypesSecond : businessTypes.slice(), // 如果有计算出的值就用，否则复制业务类型列表
          applicationGroups: relatedGroups.length > 0 ? relatedGroups : [''],
          payee: pool.payee ?? '', // 收款方，从后端数据中获取
          paymentRate: pool.paymentRate || 0, // 付款比例，从后端数据中获取
          schemeAutoCommit: pool.schemeAutoCommit || false, // 是否方案自动提报，从后端数据中获取
          bidAutoPush: pool.bidAutoPush || false, // 是否自动竞价，从后端数据中获取
          fullServiceRemark: pool.fullServiceRemark || '', // 全单服务费说明，从后端数据中获取
          isBasePriceMerchant: pool.isBasePriceMerchant || false, // 是否底价服务商，从后端数据中获取
        });
      });

      // 如果没有资源池卡片，添加一个默认的
      if (resourcePoolCards.value.length === 0) {
        resourcePoolCards.value = [
          {
            id: 1,
            name: '', // 资源池名称 - 移除默认值
            resourcePools: [
              {
                matchRule: '',
                matchValue: '',
                matchValueType: '',
                matchDescription: '',
                matchKey: '',
                selectedCheckboxValues: [],
              },
            ],
            businessTypes: [],
            serviceFee: '0',
            businessTypesSecond: [],
            applicationGroups: [''],
            payee: '', // 收款方
            paymentRate: 0, // 付款比例
            schemeAutoCommit: false, // 是否方案自动提报 - 改为布尔类型
            bidAutoPush: false, // 是否自动竞价 - 改为布尔类型
            fullServiceRemark: '', // 全单服务费说明
            isBasePriceMerchant: false, // 是否底价服务商
          },
        ];
      }
    }

    // 确保至少有一个空的用户选择
    if (selectedUsers.value.length === 0) {
      addUserSelection();
    }

    // 初始化完成后更新业务类型配置状态
    nextTick(() => {
      updateBusinessTypeConfigStatus();
    });
  }
};

// 富文本编辑器内容变化处理函数
const onEditorChange = (editor: any) => {
  (processOrchestration.value as any).processNotice = editor.getHtml();
};

// 计算属性判断是否为编辑模式
const isEditMode = computed(() => {
  return id.value && id.value > 0;
});

const processOrchestration = ref<IExtendedProcessOrchestration>({});

// 表单验证规则
const formRules: any = {
  name: [
    { required: true, message: '请输入流程名称', trigger: 'blur' },
    { min: 1, max: 50, message: '流程名称长度应在1-50个字符之间', trigger: 'blur' },
  ],
  description: [
    { required: true, message: '请输入流程简介', trigger: 'blur' },
    { min: 1, max: 200, message: '流程简介长度应在1-200个字符之间', trigger: 'blur' },
  ],
  prefix: [
    { required: true, message: '请输入订单编号前缀', trigger: 'blur' },
    { min: 1, max: 20, message: '订单编号前缀长度应在1-20个字符之间', trigger: 'blur' },
  ],
  processNotice: [
    { required: true, message: '请输入流程须知', trigger: 'blur' },
    { min: 1, message: '流程须知不能为空', trigger: 'blur' },
  ],
  users: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        // 检查是否有适用用户配置
        if (!selectedUsers.value || selectedUsers.value.length === 0) {
          callback(new Error('请至少配置一个适用用户'));
          return;
        }

        // 检查每个用户配置是否完整
        for (let i = 0; i < selectedUsers.value.length; i++) {
          const user = selectedUsers.value[i];

          // 检查是否选择了用户类型
          if (!user.type || user.type.trim() === '') {
            callback(new Error(`适用用户${i + 1}：请选择用户类型`));
            return;
          }

          // 根据用户类型检查是否需要选择具体用户
          const ruleMetadata = roleRuleOptions.value.find((rule) => rule.ruleMetaKey === user.type);
          if (ruleMetadata) {
            // 对于"集团内用户均可"类型，不需要选择具体用户
            if (ruleMetadata.ruleMetaName.includes('集团内用户均可')) {
              continue;
            }

            // 对于其他类型，需要检查是否选择了具体用户
            if (!user.user ||
                (Array.isArray(user.user) && user.user.length === 0) ||
                (typeof user.user === 'string' && user.user.trim() === '')) {
              callback(new Error(`适用用户${i + 1}：请选择具体的用户或组织`));
              return;
            }
          }
        }

        callback();
      },
      trigger: 'change',
    },
  ],
};

// 资源组相关数据
const resourceGroups = ref<
  Array<{
    name: string;
    configStatus: Record<string, string>;
  }>
>([
  {
    name: '',
    configStatus: {},
  },
]);

// 资源池相关数据
const resourcePools = ref<
  Array<{
    matchRule: string;
    matchValue: string;
    matchValueType?: string;
    matchDescription?: string;
    matchKey?: string;
    selectedCheckboxValues?: string[];
  }>
>([
  {
    matchRule: '按服务商类型匹配',
    matchValue: 'A标签、B标签',
  },
  {
    matchRule: '按服务商类型匹配',
    matchValue: 'C标签、D标签',
  },
  {
    matchRule: '选中指定服务商',
    matchValue: '香格里拉',
  },
  {
    matchRule: '排除指定供应商',
    matchValue: '蓝海大酒店',
  },
]);

// 添加资源池
const addResourcePool = (cardIndex: number) => {
  resourcePoolCards.value[cardIndex].resourcePools.push({
    matchRule: '',
    matchValue: '',
    matchValueType: '',
    matchDescription: '',
    matchKey: '',
    selectedCheckboxValues: [],
  });

  // 添加资源池后更新状态
  updateBusinessTypeConfigStatus();
};

// 移除资源池
const removeResourcePool = (poolIndex: number) => {
  const cardIndex = resourcePoolCards.value.findIndex((card) => card.resourcePools.length > poolIndex);

  if (cardIndex >= 0) {
    // 确保当前资源池卡片中有多个匹配规则时才允许删除
    if (resourcePoolCards.value[cardIndex].resourcePools.length > 1) {
      resourcePoolCards.value[cardIndex].resourcePools.splice(poolIndex, 1);
    } else {
      message.warning('至少需要保留一个匹配规则');
    }
  }
};

// 资源池组
const resourcePoolCards = ref<
  Array<{
    id: number;
    name: string; // 资源池名称
    resourcePools: Array<{
      matchRule: string;
      matchValue: string;
      matchValueType?: string;
      matchDescription?: string;
      matchKey?: string;
      selectedCheckboxValues?: string[];
    }>;
    businessTypes: string[];
    serviceFee: string;
    businessTypesSecond: string[];
    applicationGroups: string[];
    payee: number | string; // 收款方
    paymentRate: number; // 付款比例
    schemeAutoCommit: boolean; // 是否方案自动提报 - 改为布尔类型
    bidAutoPush: boolean; // 是否自动竞价 - 改为布尔类型
    fullServiceRemark?: string; // 全单服务费说明
    isBasePriceMerchant: boolean; // 是否底价服务商
  }>
>([
  {
    id: 1,
    name: '', // 资源池名称 - 移除默认值
    resourcePools: [
      {
        matchRule: '按服务商类型匹配',
        matchValue: 'A标签、B标签',
      },
      {
        matchRule: '按服务商类型匹配',
        matchValue: 'C标签、D标签',
      },
      {
        matchRule: '选中指定服务商',
        matchValue: '香格里拉',
      },
      {
        matchRule: '排除指定供应商',
        matchValue: '蓝海大酒店',
      },
    ],
    businessTypes: ['住宿', '全单服务费'],
    serviceFee: '0',
    businessTypesSecond: ['住宿'],
    applicationGroups: [''], // 移除默认值
    payee: '', // 收款方
    paymentRate: 0, // 付款比例
    schemeAutoCommit: false, // 是否方案自动提报 - 改为布尔类型
    bidAutoPush: false, // 是否自动竞价 - 改为布尔类型
    fullServiceRemark: '', // 全单服务费说明
    isBasePriceMerchant: false, // 是否底价服务商
  },
]);

// 添加新的资源池卡片
const addResourcePoolCard = () => {
  const newId =
    resourcePoolCards.value.length > 0 ? Math.max(...resourcePoolCards.value.map((card) => card.id)) + 1 : 1;

  resourcePoolCards.value.push({
    id: newId,
    name: '', // 资源池名称 - 移除默认值
    resourcePools: [
      {
        matchRule: '',
        matchValue: '',
        matchValueType: '',
        matchDescription: '',
        matchKey: '',
        selectedCheckboxValues: [],
      },
    ],
    businessTypes: [],
    serviceFee: '0',
    businessTypesSecond: [],
    applicationGroups: [''],
    payee: '', // 收款方
    paymentRate: 0, // 付款比例
    schemeAutoCommit: false, // 是否方案自动提报 - 改为布尔类型
    bidAutoPush: false, // 是否自动竞价 - 改为布尔类型
    fullServiceRemark: '', // 全单服务费说明
    isBasePriceMerchant: false, // 是否底价服务商
  });

  // 添加资源池卡片后更新状态
  updateBusinessTypeConfigStatus();
};

// 移除资源池卡片
const removeResourcePoolCard = (cardIndex: number) => {
  if (resourcePoolCards.value.length > 1) {
    resourcePoolCards.value.splice(cardIndex, 1);
    // 移除资源池卡片后更新状态
    updateBusinessTypeConfigStatus();
  } else {
    message.warning('至少需要保留一个资源池');
  }
};

// 添加应用分组
const addApplicationGroup = (cardIndex: number) => {
  resourcePoolCards.value[cardIndex].applicationGroups.push('');
};

// 移除应用分组
const removeApplicationGroup = (cardIndex: number, groupIndex: number) => {
  const groups = resourcePoolCards.value[cardIndex].applicationGroups;
  if (groups.length > 1) {
    groups.splice(groupIndex, 1);
  } else {
    message.warning('至少需要保留一个应用分组');
  }
};

// 匹配结果相关数据
const matchResults = ref<any[]>([]);
const drawerVisible = ref(false);
const drawerTitle = ref('匹配结果详情');
const drawerLoading = ref(false);
const isClosing = ref(false); // 添加关闭状态标记

// 分页相关数据
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 匹配结果表格列定义
const matchResultColumns = ref([
  {
    title: '服务商名称',
    dataIndex: 'merchantName',
    key: 'merchantName',
  },
  {
    title: '服务商编码',
    dataIndex: 'merchantCode',
    key: 'merchantCode',
  },
  {
    title: '服务商类型',
    dataIndex: 'merchantType',
    key: 'merchantType',
    customRender: ({ text }: { text: number }) => MerchantTypeMap[text as MerchantTypeEnum] || '-',
  },
  {
    title: '评分',
    dataIndex: 'score',
    key: 'score',
    sorter: (a: { score: number }, b: { score: number }) => a.score - b.score,
  },
]);

// 查看匹配结果
const viewMatchResult = (cardIndex: number, ruleIndex: number, pageNum: number = 1, pageSize: number = 10) => {
  const pool = resourcePoolCards.value[cardIndex].resourcePools[ruleIndex];

  // 获取规则key
  const ruleKey = pool.matchRule || '未选择规则';

  // 获取匹配值
  let matchValue = '';
  let hasValue = false; // 添加标志位判断是否有有效值

  // 根据不同的匹配值类型进行处理，与viewAllMatchResults保持一致
  if (pool.matchValueType === 'CHECKBOX' && Array.isArray(pool.selectedCheckboxValues)) {
    // 对于复选框，检查是否有选中的值
    hasValue = pool.selectedCheckboxValues && pool.selectedCheckboxValues.length > 0;
    matchValue = pool.selectedCheckboxValues.join(',');
  } else if (ruleKey === 'poolNameExcludeCondition' || ruleKey === 'poolNameIncludeCondition') {
    // 针对 poolNameExcludeCondition 和 poolNameIncludeCondition 规则，确保将数组转换为字符串
    if (Array.isArray(pool.matchValue)) {
      const filteredValues = pool.matchValue.filter((v) => v && String(v).trim() !== '');
      hasValue = filteredValues.length > 0;
      matchValue = filteredValues.join(',');
    } else {
      hasValue = Boolean(pool.matchValue) && pool.matchValue !== '未设置值' && String(pool.matchValue).trim() !== '';
      matchValue = String(pool.matchValue || '');
    }
  } else if (ruleKey && (ruleKey.includes('SELECT') || ruleKey.includes('服务商'))) {
    // 处理其他服务商相关的规则
    if (Array.isArray(pool.matchValue)) {
      const filteredValues = pool.matchValue.filter((v) => v && String(v).trim() !== '');
      hasValue = filteredValues.length > 0;
      matchValue = filteredValues.join(',');
    } else {
      hasValue = Boolean(pool.matchValue) && pool.matchValue !== '未设置值' && String(pool.matchValue).trim() !== '';
      matchValue = String(pool.matchValue || '');
    }
  } else {
    // 其他类型，直接使用matchValue
    hasValue = Boolean(pool.matchValue) && pool.matchValue !== '未设置值' && String(pool.matchValue).trim() !== '';
    matchValue = String(pool.matchValue || '');
  }

  // 特殊判断：如果是酒店选择，直接从页面状态获取
  if (ruleKey.includes('roleHotelFactor') || pool.matchDescription?.includes('酒店')) {
    hasValue = true;
    matchValue = String(pool.matchValue || '酒店');
  }

  // 检查是否有有效的匹配值
  if (!hasValue) {
    message.warning('请先设置匹配值后再查看匹配结果');
    return;
  }

  // 构建请求参数，添加分页参数
  const requestData = {
    requestList: [
      {
        ruleMetaKey: ruleKey,
        ruleMetaParamJson: matchValue,
      },
    ],
    pageNum,
    pageSize,
  } as unknown as IProcessOrchestration;

  // 保存当前请求数据用于分页
  currentRequestData.value = {
    type: 'single',
    cardIndex,
    ruleIndex,
    requestList: [
      {
        ruleMetaKey: ruleKey,
        ruleMetaParamJson: matchValue,
      },
    ],
  };

  // 设置抽屉标题和加载状态
  drawerTitle.value = `匹配结果 - ${pool.matchDescription || '规则'}`;
  drawerLoading.value = true;
  isClosing.value = false; // 重置关闭状态

  // 重置分页状态（仅在第一页时重置）
  if (pageNum === 1) {
    pagination.value.current = 1;
  }

  // 调用接口获取匹配结果
  processOrchestrationApi
    .listConformMerchant(requestData)
    .then((res) => {
      handleApiResponse(res, 'viewMatchResult');
    })
    .catch((error) => {
      console.error('获取匹配结果失败:', error);
      message.error('获取匹配结果失败');
    })
    .finally(() => {
      drawerLoading.value = false;
    });
};

// 存储当前查询的请求数据，用于分页查询
const currentRequestData = ref<any>(null);

// 处理API响应数据的统一函数
const handleApiResponse = (res: any, apiName: string) => {
  const responseData = res as any;

  if (responseData && responseData.data) {
    matchResults.value = responseData.data.records || [];
    // 更新分页信息
    pagination.value.current = responseData.data.pageNum || 1;
    pagination.value.pageSize = responseData.data.pageSize || 10;
    pagination.value.total = responseData.data.total || 0;
  } else {
    // 兼容旧的返回格式 - 直接在根级别的情况
    matchResults.value = responseData?.records || [];
    pagination.value.current = responseData?.pageNum || 1;
    pagination.value.pageSize = responseData?.pageSize || 10;
    pagination.value.total = responseData?.total || matchResults.value.length;
  }

  // 显示抽屉
  drawerVisible.value = true;
};

// 查看全部匹配结果
const viewAllMatchResults = (cardIndex: number, pageNum: number = 1, pageSize: number = 10) => {
  const pools = resourcePoolCards.value[cardIndex].resourcePools;

  // 如果没有规则，显示提示
  if (!pools || pools.length === 0) {
    message.info('当前资源池没有配置匹配规则');
    return;
  }

  // 构建所有规则的请求列表
  const requestList = pools.map((pool) => {
    // 获取规则key
    const ruleKey = pool.matchRule || '未选择规则';

    // 获取匹配值
    let matchValue = '';
    if (pool.matchValueType === 'CHECKBOX' && Array.isArray(pool.selectedCheckboxValues)) {
      // 对于复选框，直接使用选中的value值数组
      matchValue = pool.selectedCheckboxValues.join(',');
    } else if (ruleKey === 'poolNameExcludeCondition' || ruleKey === 'poolNameIncludeCondition') {
      // 针对 poolNameExcludeCondition 和 poolNameIncludeCondition 规则，确保将数组转换为字符串
      if (Array.isArray(pool.matchValue)) {
        matchValue = pool.matchValue.join(',');
      } else {
        matchValue = String(pool.matchValue || '');
      }
    } else if (pool.matchRule && (pool.matchRule.includes('SELECT') || pool.matchRule.includes('服务商'))) {
      // 处理其他服务商相关的规则
      if (Array.isArray(pool.matchValue)) {
        matchValue = pool.matchValue.join(',');
      } else {
        matchValue = String(pool.matchValue || '');
      }
    } else {
      // 对于其他类型，直接使用matchValue
      matchValue = pool.matchValue || '未设置值';
    }

    return {
      ruleMetaKey: ruleKey,
      ruleMetaParamJson: matchValue,
    };
  });

  // 构建请求参数，添加分页参数
  const requestData = {
    requestList,
    pageNum,
    pageSize,
  } as unknown as IProcessOrchestration;

  // 保存当前请求数据用于分页
  currentRequestData.value = { cardIndex, requestList };

  // 设置抽屉标题和加载状态
  drawerTitle.value = '全部规则匹配结果';
  drawerLoading.value = true;

  // 重置分页状态（仅在第一页时重置）
  if (pageNum === 1) {
    pagination.value.current = 1;
  }

  // 调用接口获取匹配结果
  processOrchestrationApi
    .listConformMerchant(requestData)
    .then((res) => {
      handleApiResponse(res, 'viewAllMatchResults');
    })
    .catch((error) => {
      message.error('获取全部匹配结果失败');
    })
    .finally(() => {
      drawerLoading.value = false;
    });
};

// 添加资源组
const addResourceGroup = () => {
  resourceGroups.value.push({
    name: '', // 移除默认名称
    configStatus: {},
  });
  // 添加资源组后更新状态
  updateBusinessTypeConfigStatus();
};

// 移除资源组
const removeResourceGroup = (index: number) => {
  resourceGroups.value.splice(index, 1);
  // 移除资源组后更新状态
  updateBusinessTypeConfigStatus();
};

// 修改业务类型选项 - 创建过滤后的选项
const getFilteredBusinessTypeOptions = (isFirst = false) => {
  const options = ProcessOrchestrationServiceTypeEnum.getTypeOptions();
  if (!isFirst) {
    // 非第一个业务类型过滤掉全单服务费
    return options.filter((option) => {
      const label = option.label;
      return label !== '全单服务费';
    });
  }
  // 主要业务类型选项
  return options.filter((option)=>{
    const label = option.label;
    return label !== '平台服务费';
  });
};

// 主要业务类型选项（包含全单服务费）
const businessTypeOptions = ref(getFilteredBusinessTypeOptions(true));

// 次要业务类型选项（不包含全单服务费）
const secondaryBusinessTypeOptions = ref(getFilteredBusinessTypeOptions(false));

// 收款方选项
const payeeOptions = ref([
  { label: '国旅', value: 0 },
  { label: '供应商', value: 1 },
]);

// 获取资源池卡片的第一个业务类型选项，只显示主业务类型中已选择的选项
const getPoolFirstBusinessTypeOptions = () => {

  if (!Array.isArray(processOrchestration.value.items) || processOrchestration.value.items.length === 0) {
    return [];
  }
  console.log(processOrchestration.value,"processOrchestration.value");
  

  // 过滤出主业务类型中选择的选项（包括全单服务费）
  return processOrchestration.value.items
    .map((item) => ({
      label: item,
      value: item,
    }));
};

// 计算属性：根据主业务类型的选择生成资源池第一个业务类型的选项
const filteredPoolFirstBusinessTypeOptions = computed(() => {
  return getPoolFirstBusinessTypeOptions();
});

// 检查资源池是否选择了全单服务费
const hasPoolFullServiceFee = (cardIndex: number) => {
  const card = resourcePoolCards.value[cardIndex];
  return Array.isArray(card.businessTypes) && card.businessTypes.includes('全单服务费');
};

// 检查是否选择了全单服务费
const hasFullServiceFee = computed(() => {
  return Array.isArray(processOrchestration.value.items) && processOrchestration.value.items.includes('全单服务费');
});



// 获取配置状态样式类名
const getStatusClass = (status: string): string => {
  switch (status) {
    case '已配置':
      return 'status-success';
    case '重复':
      return 'status-error';
    case '缺失':
      return 'status-warning';
    default:
      return 'status-default';
  }
};

// 原来的获取配置状态样式方法可以保留用于兼容性
const getStatusStyle = (status: string): Record<string, string> => {
  switch (status) {
    case '已配置':
      return { color: '#52c41a' };
    case '重复':
      return { color: '#ff4d4f' };
    case '缺失':
      return { color: '#faad14' };
    default:
      return { color: '#999' };
  }
};

// 检查是否存在缺失的业务类型配置
const checkMissingBusinessTypes = (): boolean => {
  // 如果没有选择业务类型，则不进行检查
  if (!Array.isArray(processOrchestration.value.items) || processOrchestration.value.items.length === 0) {
    return false;
  }

  // 遍历所有资源组，检查是否有缺失的业务类型
  for (const group of resourceGroups.value) {
    // 检查每个业务类型的配置状态
    for (const [type, status] of Object.entries(group.configStatus)) {
      // 跳过全单服务费的检查
      if (type === '全单服务费') {
        continue;
      }

      if (status === '缺失') {
        return true; // 发现缺失的业务类型
      }
    }
  }

  return false; // 没有缺失的业务类型
};

// 验证资源池和资源组的必填字段
const validateResourcePoolsAndGroups = (): { isValid: boolean; message: string } => {
  // 检查资源组名称
  for (let i = 0; i < resourceGroups.value.length; i++) {
    const group = resourceGroups.value[i];
    if (!group.name || group.name.trim() === '') {
      return {
        isValid: false,
        message: `资源组${i + 1}的名称不能为空，请填写资源组名称`
      };
    }
  }

  // 检查资源池名称和应用分组
  for (let i = 0; i < resourcePoolCards.value.length; i++) {
    const card = resourcePoolCards.value[i];

    // 检查资源池名称
    if (!card.name || card.name.trim() === '') {
      return {
        isValid: false,
        message: `资源池${i + 1}的名称不能为空，请填写资源池名称`
      };
    }

    // 检查应用分组 - 只检查非空的应用分组
    const validGroups = card.applicationGroups.filter(group => group && group.trim() !== '');
    if (validGroups.length === 0) {
      return {
        isValid: false,
        message: `资源池"${card.name}"至少需要选择一个应用分组`
      };
    }

    // 检查是否有空的应用分组项
    for (let j = 0; j < card.applicationGroups.length; j++) {
      const group = card.applicationGroups[j];
      if (!group || group.trim() === '') {
        return {
          isValid: false,
          message: `资源池"${card.name}"的应用分组${j + 1}不能为空，请选择应用分组或移除该项`
        };
      }
    }
  }

  return { isValid: true, message: '' };
};

// 验证收款方和付款比例的必填性
const validatePaymentFields = (): { isValid: boolean; message: string } => {
  for (let i = 0; i < resourcePoolCards.value.length; i++) {
    const card = resourcePoolCards.value[i];

    // 收款方必填
    if (card.payee === '' || card.payee === null || card.payee === undefined) {
      return {
        isValid: false,
        message: `资源池"${card.name || `资源池${i + 1}`}"的收款方为必填项，请选择收款方`
      };
    }

    // 付款比例必填（可以为0）
    if (card.paymentRate === null || card.paymentRate === undefined || card.paymentRate === '') {
      return {
        isValid: false,
        message: `资源池"${card.name || `资源池${i + 1}`}"的付款比例为必填项，请输入付款比例`
      };
    }

    // 检查付款比例是否超过100%
    if (card.paymentRate > 100) {
      return {
        isValid: false,
        message: `资源池"${card.name || `资源池${i + 1}`}"的付款比例不能超过100%`
      };
    }
  }

  return { isValid: true, message: '' };
};

// 添加emit定义
const emit = defineEmits(['edit-complete']);

// 打开导入弹窗
const openImportModal = () => {
  if (isViewMode.value) {
    message.warning('查看模式下不能导入数据');
    return;
  }
  importModalVisible.value = true;
  importData.value = '';
};

// 关闭导入弹窗
const closeImportModal = () => {
  importModalVisible.value = false;
  importData.value = '';
};

// 执行导入
const handleImport = async () => {
  if (!importData.value.trim()) {
    message.error('请先粘贴数据');
    return;
  }

  try {
    importLoading.value = true;

    // 解析JSON数据
    const parsedData = JSON.parse(importData.value);
    console.log('解析的导入数据:', parsedData);

    // 将解析的数据填充到表单中
    await fillFormWithImportedData(parsedData);

    message.success('数据导入成功');
    importModalVisible.value = false;
    importData.value = '';
  } catch (error) {
    console.error('导入数据解析失败:', error);
    message.error('数据格式错误，请检查粘贴的数据是否正确');
  } finally {
    importLoading.value = false;
  }
};

// 填充表单数据
const fillFormWithImportedData = async (data: any) => {
  try {
    // 基本信息填充
    processOrchestration.value.name = data.name || '';
    processOrchestration.value.description = data.description || '';
    processOrchestration.value.prefix = data.prefix || '';
    processOrchestration.value.isHotelDemandSubmittable = data.isHotelDemandSubmittable || false;
    processOrchestration.value.supportInternational = data.supportInternational || false;
    processOrchestration.value.processNotice = data.processNotice || '';
    processOrchestration.value.platformFeeRate = data.platformFeeRate || 0;

    // 处理业务类型 - 从bitmap转换为字符串数组
    if (typeof data.items === 'number') {
      const itemsBitmap = data.items;
      const itemsArray: string[] = [];

      Object.entries(ProcessOrchestrationServiceTypeEnum).forEach(([key, value]) => {
        if (typeof value === 'number') {
          if ((itemsBitmap & value) === value) {
            const typeText = ProcessOrchestrationServiceTypeEnum.getTypeText(value);
            if (typeText !== '未知') {
              itemsArray.push(typeText);
            }
          }
        }
      });

      processOrchestration.value.items = itemsArray;
    }

    // 处理适用用户数据
    if (Array.isArray(data.consumers) && data.consumers.length > 0) {
      selectedUsers.value = data.consumers.map((consumer: any) => {
        let userValue: any = [];

        // 根据不同的规则类型进行差异化处理（复用现有的get方法中的逻辑）
        switch (consumer.ruleMetaKey) {
          case 'roleExternalEnterpriseCondition':
            try {
              let enterpriseCodes: string[] = [];
              if (consumer.ruleMetaParamJson) {
                if (consumer.ruleMetaParamJson.startsWith('[')) {
                  enterpriseCodes = JSON.parse(consumer.ruleMetaParamJson);
                } else {
                  enterpriseCodes = consumer.ruleMetaParamJson
                    .split(',')
                    .map((code: string) => code.trim())
                    .filter(Boolean);
                }
              }

              let enterpriseData: any[] = [];
              if (consumer.reverseDisplayParam) {
                try {
                  const parsedReverseValue = JSON.parse(consumer.reverseDisplayParam);
                  if (Array.isArray(parsedReverseValue)) {
                    enterpriseData = parsedReverseValue;
                  }
                } catch (e) {
                  console.error('解析reverseDisplayParam失败:', e);
                }
              }

              userValue = enterpriseCodes;
              return {
                type: consumer.ruleMetaKey || '',
                user: enterpriseCodes,
                enterpriseData: enterpriseData,
              };
            } catch (e) {
              console.error('解析集团外企业数据失败:', e);
              userValue = [];
            }
            break;

          case 'roleDepartIncludeCondition':
            try {
              let departmentCodes: string[] = [];
              if (consumer.ruleMetaParamJson) {
                if (consumer.ruleMetaParamJson.startsWith('[')) {
                  departmentCodes = JSON.parse(consumer.ruleMetaParamJson);
                } else {
                  departmentCodes = consumer.ruleMetaParamJson
                    .split(',')
                    .map((code: string) => code.trim())
                    .filter(Boolean);
                }
              }

              let deptData: any[] = [];
              if (consumer.reverseDisplayParam) {
                try {
                  const parsedReverseValue = JSON.parse(consumer.reverseDisplayParam);
                  if (Array.isArray(parsedReverseValue)) {
                    deptData = parsedReverseValue;
                  }
                } catch (e) {
                  console.error('解析部门reverseDisplayParam失败:', e);
                }
              }

              userValue = departmentCodes;
              return {
                type: consumer.ruleMetaKey || '',
                user: departmentCodes,
                deptData: deptData,
              };
            } catch (e) {
              console.error('解析集团内部门数据失败:', e);
              userValue = [];
            }
            break;

          case 'roleUsernameIncludeCondition':
            try {
              if (consumer.reverseDisplayParam) {
                try {
                  const parsedReverseValue = JSON.parse(consumer.reverseDisplayParam);
                  if (Array.isArray(parsedReverseValue)) {
                    userValue = parsedReverseValue;
                  }
                } catch (e) {
                  console.error('解析用户reverseDisplayParam失败:', e);
                }
              } else {
                const usernames = JSON.parse(consumer.ruleMetaParamJson || '[]');
                userValue = usernames.map((username: string) => ({
                  username,
                  nickName: username,
                  id: username,
                }));
              }
            } catch (e) {
              console.error('解析指定集团内员工工号数据失败:', e);
              userValue = [];
            }
            break;

          case 'roleUsernameAllCondition':
            userValue = [];
            break;

          default:
            try {
              if (consumer.reverseDisplayParam) {
                try {
                  const parsedReverseValue = JSON.parse(consumer.reverseDisplayParam || '[]');
                  if (Array.isArray(parsedReverseValue)) {
                    userValue = parsedReverseValue;
                  } else {
                    userValue = consumer.reverseDisplayParam;
                  }
                } catch (e) {
                  userValue = consumer.reverseDisplayParam;
                }
              } else if (typeof consumer.ruleMetaParamJson === 'string') {
                try {
                  const parsedValue = JSON.parse(consumer.ruleMetaParamJson);
                  if (Array.isArray(parsedValue)) {
                    userValue = parsedValue.map((username: string) => ({
                      username,
                      nickName: username,
                      id: username,
                    }));
                  } else {
                    userValue = [
                      {
                        username: String(consumer.ruleMetaParamJson),
                        nickName: String(consumer.ruleMetaParamJson),
                        id: String(consumer.ruleMetaParamJson),
                      },
                    ];
                  }
                } catch (e) {
                  userValue = [
                    {
                      username: String(consumer.ruleMetaParamJson),
                      nickName: String(consumer.ruleMetaParamJson),
                      id: String(consumer.ruleMetaParamJson),
                    },
                  ];
                }
              } else {
                userValue = String(consumer.ruleMetaParamJson);
              }
            } catch (e) {
              userValue = [
                {
                  username: String(consumer.ruleMetaParamJson),
                  nickName: String(consumer.ruleMetaParamJson),
                  id: String(consumer.ruleMetaParamJson),
                },
              ];
            }
        }

        return {
          type: consumer.ruleMetaKey || '',
          user: userValue,
        };
      });
    } else {
      // 如果没有用户数据，确保至少有一个空的用户选择
      if (selectedUsers.value.length === 0) {
        addUserSelection();
      }
    }

    // 处理全局平台服务费配置（导入时）
    if (data.platformFeeRate !== undefined) {
      processOrchestration.value.platformFeeRate = data.platformFeeRate;
    }

    // 处理资源组数据
    if (Array.isArray(data.merchantGroups) && data.merchantGroups.length > 0) {
      resourceGroups.value = data.merchantGroups.map((group: any) => {
        return {
          name: group.name || '',
          serviceFeeRate: 0, // 不再从资源组中读取费率
          businessTypes: [], // 不再从资源组中读取业务类型
          configStatus: {},
        };
      });
    }

    // 处理资源池数据
    if (Array.isArray(data.merchantPools) && data.merchantPools.length > 0) {
      resourcePoolCards.value = [];

      data.merchantPools.forEach((pool: any, index: number) => {
        // 构建匹配规则数组（复用现有的get方法中的逻辑）
        const resourcePoolsData =
          Array.isArray(pool.matches) && pool.matches.length > 0
            ? pool.matches.map((match: any) => {
                let matchValue = match.ruleMetaParamJson || '';
                let selectedCheckboxValues: string[] = [];
                let matchValueType = '';
                let matchDescription = '';
                let matchKey = '';

                const ruleConfig = matchRuleOptions.value.find(
                  (rule: any) => rule.configs && rule.configs.key === match.ruleMetaKey,
                );

                if (ruleConfig && ruleConfig.configs) {
                  matchValueType = ruleConfig.configs.type || '';
                  matchDescription = ruleConfig.configs.description || '';
                  matchKey = ruleConfig.configs.key || '';
                }

                // 处理特殊类型的值（复用现有逻辑）
                if (match.ruleMetaKey === 'poolNameExcludeCondition') {
                  try {
                    const parsedValue = JSON.parse(matchValue);
                    if (Array.isArray(parsedValue)) {
                      matchValue = parsedValue;
                    }
                  } catch (e) {
                    matchValue = matchValue
                      .split(',')
                      .map((v: string) => v.trim())
                      .filter(Boolean);
                  }
                  matchValueType = 'SELECT';
                } else if (match.ruleMetaKey === 'poolNameIncludeCondition') {
                  try {
                    const parsedValue = JSON.parse(matchValue);
                    if (Array.isArray(parsedValue)) {
                      matchValue = parsedValue;
                    } else {
                      matchValue = matchValue
                        .split(',')
                        .map((v: string) => v.trim())
                        .filter(Boolean);
                    }
                  } catch (e) {
                    matchValue = matchValue
                      .split(',')
                      .map((v: string) => v.trim())
                      .filter(Boolean);
                  }
                  matchValueType = 'SELECT';
                } else if (match.ruleMetaKey === 'poolTypeCondition') {
                  try {
                    selectedCheckboxValues = matchValue
                      .split(',')
                      .map((v: any) => v.trim())
                      .filter(Boolean);

                    const labelText = getCheckboxLabelsFromValues(match.ruleMetaKey, selectedCheckboxValues);
                    if (labelText) {
                      matchValue = labelText;
                    }
                  } catch (e) {
                    selectedCheckboxValues = [];
                  }
                  matchValueType = 'CHECKBOX';
                } else if (
                  match.ruleMetaKey &&
                  (match.ruleMetaKey.includes('SELECT') || match.ruleMetaKey.includes('服务商'))
                ) {
                  try {
                    const parsedValue = parseServiceProviderValue(matchValue);
                    matchValue = parsedValue;
                    matchValueType = 'SELECT';
                  } catch (e) {
                    console.error('解析服务商数据失败:', e);
                  }
                } else if (match.ruleMetaParamJson) {
                  selectedCheckboxValues = match.ruleMetaParamJson
                    .split(',')
                    .map((v: string) => v.trim())
                    .filter(Boolean);
                }

                if (match.reverseDisplayParam) {
                  try {
                    const reverseData = JSON.parse(match.reverseDisplayParam);
                    if (Array.isArray(reverseData) && reverseData.length > 0) {
                      if (
                        match.ruleMetaKey === 'poolNameExcludeCondition' ||
                        match.ruleMetaKey === 'poolNameIncludeCondition'
                      ) {
                        matchValue = reverseData.map((item: any) => item.code);
                      }
                    }
                  } catch (e) {
                    console.error('解析reverseDisplayParam失败:', e);
                  }
                }

                return {
                  matchRule: match.ruleMetaKey || '',
                  matchValue: matchValue,
                  matchValueType: matchValueType,
                  matchDescription: matchDescription,
                  matchKey: matchKey,
                  selectedCheckboxValues: selectedCheckboxValues,
                };
              })
            : [
                {
                  matchRule: '',
                  matchValue: '',
                  matchValueType: '',
                  matchDescription: '',
                  matchKey: '',
                  selectedCheckboxValues: [],
                },
              ];

        // 提取业务类型列表
        const businessTypes = Array.isArray(pool.itemRanges)
          ? pool.itemRanges
              .map((range: any) => {
                return ProcessOrchestrationServiceTypeEnum.getTypeText(range.itemType);
              })
              .filter(Boolean)
          : [];

        const serviceFee = String(pool.fullServiceRangeRateLimit || 0);

        const relatedGroups = data.merchantGroups
          ? data.merchantGroups
              .filter((group: any) => Array.isArray(group.poolIds) && group.poolIds.includes(pool.id))
              .map((group: any) => group.name)
          : [''];

        // 根据 fullServiceRange 反推哪些业务类型被选中了作为第二个业务类型
        const businessTypesSecond: string[] = [];
        if (pool.fullServiceRange) {
          // 遍历所有可能的业务类型，检查哪些在 fullServiceRange 中
          Object.entries(ProcessOrchestrationServiceTypeEnum).forEach(([key, value]) => {
            if (typeof value === 'number' && value !== 0) {
              // 使用位运算检查该业务类型是否在 fullServiceRange 中
              if ((pool.fullServiceRange & value) === value) {
                const typeText = ProcessOrchestrationServiceTypeEnum.getTypeText(value);
                if (typeText && typeText !== '未知') {
                  businessTypesSecond.push(typeText);
                }
              }
            }
          });
        }

        resourcePoolCards.value.push({
          id: pool.id || index + 1,
          name: pool.name || (pool.id ? `资源池${index + 1}` : ''), // 编辑模式保持兼容性
          resourcePools: resourcePoolsData,
          businessTypes: businessTypes,
          serviceFee: serviceFee,
          businessTypesSecond: businessTypesSecond.length > 0 ? businessTypesSecond : businessTypes.slice(),
          applicationGroups: relatedGroups.length > 0 ? relatedGroups : [''],
          payee: pool.payee ?? '',
          paymentRate: pool.paymentRate || 0,
          schemeAutoCommit: pool.schemeAutoCommit || false,
          bidAutoPush: pool.bidAutoPush || false,
          fullServiceRemark: pool.fullServiceRemark || '', // 全单服务费说明
          isBasePriceMerchant: pool.isBasePriceMerchant || false, // 是否底价服务商
        });
      });

      if (resourcePoolCards.value.length === 0) {
        initResourcePools();
      }
    }

    // 处理节点数据
    if (Array.isArray(data.nodes) && data.nodes.length > 0) {
      // 按照 seq 属性排序节点
      const sortedNodes = [...data.nodes].sort((a: any, b: any) => {
        return (a.seq || 0) - (b.seq || 0);
      });

      // 设置所有节点默认展开状态
      sortedNodes.forEach((node: any) => {
        node.isExpanded = true;
      });

      // 将节点数据保存到 processOrchestration 中
      (processOrchestration.value as any).nodes = sortedNodes;

      console.log('节点数据已导入:', sortedNodes.length, '个节点');
    } else {
      // 如果没有节点数据，确保清空现有节点
      (processOrchestration.value as any).nodes = [];
    }

    // 使用nextTick确保更新完成后再更新配置状态
    await nextTick();
    updateBusinessTypeConfigStatus();

    console.log('数据导入完成，包含节点数据');
  } catch (error) {
    console.error('填充表单数据失败:', error);
    throw error;
  }
};

// 初始化缓存key
const initCacheKey = () => {
  const username = loginUser.value?.username || 'unknown';

  if (id.value) {
    // 规则: haierbusiness-mice-bidman_工号_流程编辑_ID
    cacheKey.value = `haierbusiness-mice-bidman_${username}_processOrchestrationEdit_${id.value}`;
  } else {
    // 新增模式，规则: haierbusiness-mice-bidman_工号_流程新增_temp
    cacheKey.value = `haierbusiness-mice-bidman_${username}_processOrchestrationNew_temp`;
  }
};

// 获取当前表单数据（包括节点数据）
const getCurrentFormData = () => {
  return {
    // 基础表单数据
    processOrchestration: processOrchestration.value,
    selectedUsers: selectedUsers.value,
    resourceGroups: resourceGroups.value,
    resourcePools: resourcePools.value,
    // 节点数据（如果存在）
    nodeData: window.processNodesData || null,
    timestamp: Date.now()
  };
};

// 自动暂存
const autoSave = async () => {
  // 只在新增模式下进行自动暂存
  if (id.value || !cacheKey.value) return;

  try {
    const formData = getCurrentFormData();

    await saveDataBy({
      applicationCode: 'haierbusiness-mice-bidman',
      cacheKey: cacheKey.value,
      cacheValue: JSON.stringify(formData),
    });

    console.log('自动暂存成功');
  } catch (error) {
    console.error('自动暂存失败:', error);
  }
};

// 手动暂存
const manualSave = async () => {
  // 只在新增模式下进行手动暂存
  if (id.value) {
    message.info('编辑模式下无需暂存');
    return;
  }

  if (!cacheKey.value) {
    message.error('缓存key未初始化');
    return;
  }

  try {
    const formData = getCurrentFormData();

    await saveDataBy({
      applicationCode: 'haierbusiness-mice-bidman',
      cacheKey: cacheKey.value,
      cacheValue: JSON.stringify(formData),
    });

  } catch (error) {
    console.error('手动暂存失败:', error);
    message.error('暂存失败，请重试');
  }
};

// 检查缓存数据
const checkCacheData = async () => {
  // 只在新增模式下检查缓存
  if (id.value || !cacheKey.value) return;

  try {
    const cachedData = await getDataBy({
      applicationCode: 'haierbusiness-mice-bidman',
      cacheKey: cacheKey.value,
    });

    if (cachedData) {
      cacheData.value = JSON.parse(cachedData);
      cacheRestoreModalVisible.value = true;
    }
  } catch (error) {
    console.error('检查缓存数据失败:', error);
  }
};

// 恢复缓存数据
const restoreCacheData = async () => {
  if (!cacheData.value) return;

  try {
    const data = cacheData.value;

    // 恢复表单数据
    processOrchestration.value = data.processOrchestration || {};
    selectedUsers.value = data.selectedUsers || [];
    resourceGroups.value = data.resourceGroups || [];
    resourcePools.value = data.resourcePools || [];

    // 恢复节点数据到全局变量，供processNodes.vue使用
    if (data.nodeData) {
      window.processNodesData = data.nodeData;
    }

    message.success('数据恢复成功！');
    cacheRestoreModalVisible.value = false;

    // 开始自动保存
    startAutoSave();
  } catch (error) {
    console.error('恢复缓存数据失败:', error);
    message.error('数据恢复失败');
  }
};

// 删除缓存数据
const deleteCacheData = async () => {
  if (!cacheKey.value) return;

  try {
    await delData({
      applicationCode: 'haierbusiness-mice-bidman',
      cacheKey: cacheKey.value,
    });

    // 同时清除节点缓存数据
    if (window.processNodesData) {
      window.processNodesData = null;
    }

    cacheRestoreModalVisible.value = false;
    message.success('缓存数据已清除');

    // 开始自动保存
    startAutoSave();
  } catch (error) {
    console.error('删除缓存数据失败:', error);
    message.error('删除缓存失败');
  }
};

// 提交成功后清除缓存数据（不显示消息，不重新开始自动保存）
const clearCacheAfterSubmit = async () => {
  if (!cacheKey.value) return;

  try {
    await delData({
      applicationCode: 'haierbusiness-mice-bidman',
      cacheKey: cacheKey.value,
    });

    // 同时清除节点缓存数据
    if (window.processNodesData) {
      window.processNodesData = null;
    }

    console.log('提交成功，缓存数据已清除');
  } catch (error) {
    console.error('清除缓存数据失败:', error);
  }
};

// 开始自动保存
const startAutoSave = () => {
  // 只在新增模式下启动自动保存
  if (id.value) return;

  // 清除现有定时器
  if (autoSaveTimer.value) {
    clearInterval(autoSaveTimer.value);
  }
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
  }

  // 设置60秒自动保存
  autoSaveTimer.value = setInterval(() => {
    autoSave();
  }, 60000);

  // 设置倒计时
  countdownTimer.value = setInterval(() => {
    countdownTime.value = countdownTime.value === 0 ? 60 : countdownTime.value - 1;
  }, 1000);
};

// 停止自动保存
const stopAutoSave = () => {
  if (autoSaveTimer.value) {
    clearInterval(autoSaveTimer.value);
    autoSaveTimer.value = null;
  }
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
    countdownTimer.value = null;
  }
};

// 接收来自processNodes.vue的节点数据更新
const updateNodeData = (nodeData: any) => {
  window.processNodesData = nodeData;
};

// 暴露方法给全局使用
window.updateProcessNodeData = updateNodeData;
window.manualSaveProcessData = manualSave;
window.deleteCacheData = deleteCacheData;
window.clearCacheAfterSubmit = clearCacheAfterSubmit;

// 跳转到详情页面的函数
const goToDetail = () => {
  if (id.value) {
    currentRouter.value.push({ path: '/bidman/processOrchestration/detail', query: { id: id.value } });
  }
};

const handleOk = async () => {
  // 如果是查看模式，直接跳转到详情页面
  if (isViewMode.value) {
    goToDetail();
    return;
  }

  // 表单验证
  try {
    await from.value?.validate();
  } catch (error) {
    console.log('表单验证失败:', error);
    return;
  }

  // 验证资源池和资源组的必填字段
  const validation = validateResourcePoolsAndGroups();
  if (!validation.isValid) {
    message.error(validation.message);
    return;
  }

  // 验证收款方和付款比例的关联性
  const paymentValidation = validatePaymentFields();
  if (!paymentValidation.isValid) {
    message.error(paymentValidation.message);
    return;
  }

  // 检查是否有缺失的业务类型配置
  if (checkMissingBusinessTypes()) {
    message.error('存在缺失的业务类型配置，请先完成所有业务类型的配置！');
    return;
  }

  confirmLoading.value = true;

  // 处理多选框的值，将selectedCheckboxValues转换为matchValue
  resourcePoolCards.value.forEach((card) => {
    card.resourcePools.forEach((pool) => {
      if (pool.matchValueType === 'CHECKBOX' && Array.isArray(pool.selectedCheckboxValues)) {
        // 将选中的值转换为字符串
        const selectedOptions =
          matchValueOptions.value[pool.matchRule]?.filter(
            (option) =>
              pool.selectedCheckboxValues?.includes(option.value) ||
              pool.selectedCheckboxValues?.includes(String(option.value)),
          ) || [];

        // 将选中的选项标签连接成字符串
        pool.matchValue = selectedOptions.map((option) => option.label).join('、');
      }
    });
  });

  // 构造用户适用规则数据 - consumers
  const consumers = selectedUsers.value
    .map((item) => {
      // 准备reverseDisplayParam字段用于前端回显
      let reverseDisplayParam = '';
      let ruleMetaParamJson = '';

      // 根据不同的类型处理数据
      switch (item.type) {
        // 集团外企业
        case 'roleExternalEnterpriseCondition':
          // 保存企业编码列表 - 转换为逗号分隔的字符串
          if (Array.isArray(item.user) && item.user.length > 0) {
            ruleMetaParamJson = item.user.join(',');
          } else {
            ruleMetaParamJson = '';
          }

          // 特定格式的企业数据用于回显
          if (item.enterpriseData && Array.isArray(item.enterpriseData)) {
            reverseDisplayParam = JSON.stringify(
              item.enterpriseData.map((enterprise) => ({
                code: enterprise.code,
                name: enterprise.name,
              })),
            );
          } else {
            // 兼容旧数据
            reverseDisplayParam = JSON.stringify(
              Array.isArray(item.user)
                ? item.user.map((code) => {
                    // 查找对应企业
                    const enterprise = enterpriseList.value.find((e) => e.code === code);
                    return {
                      code,
                      name: enterprise ? enterprise.name : code,
                    };
                  })
                : [],
            );
          }
          break;

        // 集团内部门
        case 'roleDepartIncludeCondition':
          // 保存部门编码列表 - 转换为逗号分隔的字符串
          if (Array.isArray(item.user) && item.user.length > 0) {
            ruleMetaParamJson = item.user.join(',');
          } else {
            ruleMetaParamJson = '';
          }

          // 特定格式的部门数据用于回显
          if (item.deptData && Array.isArray(item.deptData)) {
            reverseDisplayParam = JSON.stringify(
              item.deptData.map((dept) => ({
                code: dept.code,
                name: dept.name,
              })),
            );
          } else {
            // 兼容旧数据
            reverseDisplayParam = JSON.stringify(
              Array.isArray(item.user)
                ? item.user.map((code) => {
                    // 查找对应部门
                    const dept = deptList.value.find((d) => d.code?.toString() === code);
                    return {
                      code,
                      name: dept ? dept.name : code,
                    };
                  })
                : [],
            );
          }
          break;

        // 指定集团内员工工号
        case 'roleUsernameIncludeCondition':
          // 保存工号列表 - 转换为逗号分隔的字符串
          if (Array.isArray(item.user) && item.user.length > 0) {
            const usernames = item.user.map((u) => (typeof u === 'object' ? u.username || u.id : u));
            ruleMetaParamJson = usernames.join(',');
          } else {
            ruleMetaParamJson = '';
          }

          // 保存完整用户信息用于回显
          reverseDisplayParam = JSON.stringify(
            Array.isArray(item.user)
              ? item.user.map((u) => {
                  if (typeof u === 'object') {
                    return {
                      username: u.username || u.id || '',
                      nickName: u.nickName || u.username || u.id || '',
                    };
                  }
                  return {
                    username: String(u),
                    nickName: String(u),
                  };
                })
              : [],
          );
          break;

        // 集团内用户均可
        case 'roleUsernameAllCondition':
          ruleMetaParamJson = '';
          reverseDisplayParam = '[]';
          break;

        // 默认处理其他类型
        default:
          if (Array.isArray(item.user) && item.user.length > 0) {
            const values = item.user.map((u) => (typeof u === 'object' ? u.username || u.id : u));
            ruleMetaParamJson = values.join(',');
          } else if (typeof item.user === 'string' && item.user) {
            ruleMetaParamJson = item.user;
          } else {
            ruleMetaParamJson = '';
          }

          reverseDisplayParam = JSON.stringify(
            Array.isArray(item.user)
              ? item.user.map((u) => {
                  if (typeof u === 'object') {
                    return u;
                  }
                  return {
                    value: String(u),
                  };
                })
              : typeof item.user === 'string'
              ? [
                  {
                    value: item.user,
                  },
                ]
              : [],
          );
      }

      return {
        ruleMetaKey: item.type,
        ruleMetaParamJson,
        reverseDisplayParam,
      };
    })
    .filter((item) => item.ruleMetaKey); // 过滤掉没有选择规则的项

  // 构造资源池数据 - merchantPools
  const merchantPools = resourcePoolCards.value.map((card, index) => {
    // 构造匹配规则 - matches
    const matches = card.resourcePools
      .map((pool) => {
        let ruleMetaParamJson = '';
        let reverseDisplayParam = '';

        // 根据不同的规则类型正确处理参数格式
        if (pool.matchValueType === 'CHECKBOX' && Array.isArray(pool.selectedCheckboxValues)) {
          // CHECKBOX类型，使用逗号连接
          ruleMetaParamJson = pool.selectedCheckboxValues.join(',');
        } else if (pool.matchRule === 'poolNameExcludeCondition') {
          // 排除指定供应商
          if (Array.isArray(pool.matchValue)) {
            ruleMetaParamJson = JSON.stringify(pool.matchValue);

            // 构建包含供应商名称和编码的数据，用于前端回显
            const serviceProviderData = pool.matchValue.map((code: string) => {
              const provider = serviceProviderList.value.find((item) => item.code === code);
              return {
                code: code,
                name: provider ? provider.name : code, // 如果找不到供应商，则使用编码作为名称
              };
            });

            reverseDisplayParam = JSON.stringify(serviceProviderData);
          } else if (typeof pool.matchValue === 'string' && pool.matchValue) {
            try {
              const parsedValue = JSON.parse(pool.matchValue);
              ruleMetaParamJson = JSON.stringify(parsedValue);
            } catch (e) {
              ruleMetaParamJson = pool.matchValue;
            }
          } else {
            ruleMetaParamJson = '';
          }
        } else if (pool.matchRule === 'poolNameIncludeCondition') {
          // 包含指定服务商
          if (Array.isArray(pool.matchValue)) {
            // 将数组转换为逗号分隔的字符串
            ruleMetaParamJson = pool.matchValue.join(',');

            // 构建包含供应商名称和编码的数据，用于前端回显
            const serviceProviderData = pool.matchValue.map((code: string) => {
              const provider = serviceProviderList.value.find((item) => item.code === code);
              return {
                code: code,
                name: provider ? provider.name : code, // 如果找不到供应商，则使用编码作为名称
              };
            });

            reverseDisplayParam = JSON.stringify(serviceProviderData);
          } else if (typeof pool.matchValue === 'string' && pool.matchValue) {
            try {
              const parsedValue = JSON.parse(pool.matchValue);
              // 如果解析成功且是数组，转换为逗号分隔的字符串
              ruleMetaParamJson = Array.isArray(parsedValue) ? parsedValue.join(',') : pool.matchValue;
            } catch (e) {
              ruleMetaParamJson = pool.matchValue;
            }
          } else {
            ruleMetaParamJson = '';
          }
        } else if (pool.matchRule && (pool.matchRule.includes('SELECT') || pool.matchRule.includes('服务商'))) {
          // 处理其他服务商相关的规则
          if (Array.isArray(pool.matchValue)) {
            ruleMetaParamJson = JSON.stringify(pool.matchValue);

            // 构建包含供应商名称和编码的数据，用于前端回显
            const serviceProviderData = pool.matchValue.map((code: string) => {
              const provider = serviceProviderList.value.find((item) => item.code === code);
              return {
                code: code,
                name: provider ? provider.name : code,
              };
            });

            reverseDisplayParam = JSON.stringify(serviceProviderData);
          } else {
            ruleMetaParamJson = pool.matchValue || '';
          }
        } else {
          // 其他类型
          ruleMetaParamJson = pool.matchValue || '';
        }

        const matchData: any = {
          ruleMetaKey: pool.matchRule,
          ruleMetaParamJson: ruleMetaParamJson,
        };

        // 只有供应商相关的规则需要添加 reverseDisplayParam
        if (reverseDisplayParam) {
          matchData.reverseDisplayParam = reverseDisplayParam;
        }

        return matchData;
      })
      .filter((match) => match.ruleMetaKey); // 过滤掉没有选择规则的项

    // 构造业务类型范围 - itemRanges
    const itemRanges = card.businessTypes.map((itemType) => {
      // 计算当前业务类型的位运算值
      let itemTypeEnum = 0;
      Object.entries(ProcessOrchestrationServiceTypeEnum).forEach(([key, value]) => {
        if (typeof value === 'number' && ProcessOrchestrationServiceTypeEnum.getTypeText(value) === itemType) {
          itemTypeEnum = value;
        }
      });

      return {
        itemType: itemTypeEnum, // 使用枚举值而不是文本
        schemeMatchRule: '', // 默认空
      };
    });

    // 计算全单服务费相关字段（移到外层）
    let fullServiceRange = 0;
    let fullServiceRangeRateLimit = 0; // 默认为0

    // 如果选择了全单服务费业务类型
    const hasFullServiceFee = card.businessTypes.includes('全单服务费');

    if (hasFullServiceFee && Array.isArray(card.businessTypesSecond)) {
      // 计算所有选中的业务类型的位运算组合
      card.businessTypesSecond.forEach((selectedType) => {
        Object.entries(ProcessOrchestrationServiceTypeEnum).forEach(([key, value]) => {
          if (typeof value === 'number' && ProcessOrchestrationServiceTypeEnum.getTypeText(value) === selectedType) {
            fullServiceRange |= value; // 使用位运算组合
          }
        });
      });
      // 设置费率
      fullServiceRangeRateLimit = parseInt(card.serviceFee || '0');
    } else if (Array.isArray(card.businessTypesSecond) && card.businessTypesSecond.length > 0) {
      // 即使没有选择全单服务费，但如果有选择第二个业务类型，也需要计算
      card.businessTypesSecond.forEach((selectedType) => {
        Object.entries(ProcessOrchestrationServiceTypeEnum).forEach(([key, value]) => {
          if (typeof value === 'number' && ProcessOrchestrationServiceTypeEnum.getTypeText(value) === selectedType) {
            fullServiceRange |= value; // 使用位运算组合
          }
        });
      });
      // 设置费率
      fullServiceRangeRateLimit = parseInt(card.serviceFee || '0');
    }

    return {
      tempId: card.id || index + 1, // 临时ID，用于关联资源池组
      name: card.name || '', // 使用用户输入的名称，不使用默认命名
      matches,
      itemRanges,
      fullServiceRange: fullServiceRange, // 移到外层：支持服务费的业务类型枚举值
      fullServiceRangeRateLimit: fullServiceRangeRateLimit, // 移到外层：费率
      payee: card.payee ?? '', // 收款方
      paymentRate: card.paymentRate || 0, // 付款比例
      schemeAutoCommit: card.schemeAutoCommit || false, // 是否方案自动提报，从后端数据中获取
      bidAutoPush: card.bidAutoPush || false, // 是否自动竞价，从后端数据中获取
      fullServiceRemark: card.fullServiceRemark || '', // 全单服务费说明
      isBasePriceMerchant: card.isBasePriceMerchant || false, // 是否底价服务商
    };
  });

  // 构造资源组数据 - merchantGroups
  const merchantGroups = resourceGroups.value
    .map((group, index) => {
      // 获取当前组关联的资源池
      const relatedPoolIds = resourcePoolCards.value
        .filter((card) => card.applicationGroups.includes(group.name))
        .map((card) => card.id || 0);

      return {
        name: group.name || `资源组${index + 1}`,
        tempPoolIds: relatedPoolIds, // 关联资源池临时ID
      };
    })
    .filter((group) => group.name); // 过滤掉没有名称的组

  // 业务类型转换为bitmap
  let itemsBitmap = 0;
  if (Array.isArray(processOrchestration.value.items)) {
    processOrchestration.value.items.forEach((itemText: string) => {
      // 查找与文本匹配的枚举值
      Object.entries(ProcessOrchestrationServiceTypeEnum).forEach(([key, value]) => {
        if (typeof value === 'number' && ProcessOrchestrationServiceTypeEnum.getTypeText(value) === itemText) {
          itemsBitmap |= value;
        }
      });
    });
  }

  // 构造完整的提交数据对象，符合FrameworkProcessSubmitRequest格式
  const submitData = {
    id: processOrchestration.value.id || undefined,
    // pdProductLineId: 0, // 默认为0
    name: processOrchestration.value.name || '',
    description: processOrchestration.value.description || '',
    prefix: processOrchestration.value.prefix || '',
    changeContent: processOrchestration.value.modifyContent || '',
    items: itemsBitmap,
    isHotelDemandSubmittable: processOrchestration.value.isHotelDemandSubmittable || false, // 添加多酒店需求开关
    supportInternational: processOrchestration.value.supportInternational || false, // 添加国际会议提报开关
    processNotice: processOrchestration.value.processNotice || '', // 添加流程须知
    // 添加平台服务费费率配置
    platformFeeRate: parseInt(processOrchestration.value.platformFeeRate?.toString() || '0'),
    consumers,
    merchantGroups,
    merchantPools,
    nodes: (processOrchestration.value as any).nodes || [],
    // 添加编辑模式标识和现有节点数据
    isEditMode: isEditMode.value || ((processOrchestration.value as any).nodes && (processOrchestration.value as any).nodes.length > 0), // 如果有导入的节点数据，也当作编辑模式
    existingNodes: (processOrchestration.value as any).nodes || [],
    // 传递 verId 给 processNodes 组件使用
    verId: (processOrchestration.value as any).verId,
  };

  console.log('准备数据:', submitData); // 打印准备好的数据，方便调试
  emit('edit-complete', submitData);

  confirmLoading.value = false;
};

// 根据资源组动态生成应用分组选项
const resourceGroupOptions = computed(() => {
  const options = resourceGroups.value.map((group, index) => {
    // 如果有名称就使用名称，否则提示用户先填写名称
    const name = group.name && group.name.trim() !== ''
      ? group.name.trim()
      : `请先填写资源组${index + 1}名称`;

    return {
      value: group.name && group.name.trim() !== '' ? name : '', // 空名称的值为空字符串
      label: name,
      disabled: !group.name || group.name.trim() === '', // 空名称的选项禁用
    };
  });
  return options;
});

// 抽屉体样式
const drawerBodyStyle = { padding: '20px', overflow: 'hidden' };

// 处理抽屉关闭
const handleDrawerClose = () => {
  isClosing.value = true;
  window.setTimeout(() => {
    isClosing.value = false;
  }, 300);
};

// 处理分页变化
const handlePageChange = (page: number, pageSize: number) => {
  console.log('handlePageChange 被调用:', { page, pageSize, currentRequestData: currentRequestData.value });

  if (!currentRequestData.value) {
    console.log('currentRequestData 为空，退出分页处理');
    return;
  }

  console.log('当前请求类型:', currentRequestData.value.type);

  // 根据当前请求类型重新发起请求
  if (currentRequestData.value.type === 'single') {
    console.log('执行单个规则匹配结果分页');
    // 单个规则匹配结果
    viewMatchResult(currentRequestData.value.cardIndex, currentRequestData.value.ruleIndex, page, pageSize);
  } else {
    console.log('执行全部规则匹配结果分页');
    // 全部规则匹配结果
    viewAllMatchResults(currentRequestData.value.cardIndex, page, pageSize);
  }
};

// 计算和更新业务类型配置状态
const updateBusinessTypeConfigStatus = () => {
  // 如果没有选择业务类型或者资源组为空，则不执行更新
  if (
    !Array.isArray(processOrchestration.value.items) ||
    processOrchestration.value.items.length === 0 ||
    !resourceGroups.value.length
  ) {
    return;
  }

  // 遍历所有资源组
  resourceGroups.value.forEach((group) => {
    const configStatus: Record<string, string> = {};

    // 获取此资源组应用的所有资源池
    const appliedPools = resourcePoolCards.value.filter((card) => card.applicationGroups.includes(group.name));

    // 遍历所有选择的业务类型
    if (processOrchestration.value.items) {
      processOrchestration.value.items.forEach((itemType) => {
        // 跳过全单服务费
        if (itemType === '全单服务费') {
          return;
        }

        // 计算此业务类型在所有应用的资源池中的配置情况
        let configCount = 0;

        appliedPools.forEach((pool) => {
          const includes = pool.businessTypes.includes(itemType);
          if (includes) {
            configCount++;
          }
        });

        // 根据配置数量决定状态
        if (configCount === 0) {
          configStatus[itemType] = '缺失';
        } else if (configCount === 1) {
          configStatus[itemType] = '已配置';
        } else {
          configStatus[itemType] = '重复';
        }
      });
    }

    // 更新资源组的配置状态
    group.configStatus = configStatus;
  });
};

// 修复：标记用于防止无限递归
const isUpdatingBusinessTypes = ref(false);

// 修复：监听业务类型变化，更新业务类型配置状态
watch(
  () => processOrchestration.value.items,
  (newValue) => {
    // 当主业务类型变化时，更新资源池卡片的业务类型选择
    resourcePoolCards.value.forEach((card) => {
      // 过滤业务类型，只保留在新选择的业务类型中存在的选项
      if (Array.isArray(newValue)) {
        card.businessTypes = card.businessTypes.filter((type) => newValue.includes(type));
        card.businessTypesSecond = card.businessTypesSecond.filter((type) => card.businessTypes.includes(type));
      } else {
        card.businessTypes = [];
        card.businessTypesSecond = [];
      }
    });

    // 防止递归调用
    if (isUpdatingBusinessTypes.value) {
      return;
    }

    isUpdatingBusinessTypes.value = true;

    try {
      if (newValue) {
        // 当业务类型变化时，清空所有资源组的配置状态
        resourceGroups.value.forEach((group) => {
          // 初始化为空对象
          const configStatus: Record<string, string> = {};

          // 只为当前选中的业务类型设置状态，排除全单服务费
          if (Array.isArray(newValue) && newValue.length > 0) {
            newValue.forEach((itemType) => {
              // 跳过全单服务费
              if (itemType !== '全单服务费') {
                configStatus[itemType] = '缺失';
              }
            });
          }

          // 更新配置状态（这将清除已取消选择的业务类型状态）
          group.configStatus = configStatus;
        });
      } else {
        // 如果没有选择业务类型，清空所有配置状态
        resourceGroups.value.forEach((group) => {
          group.configStatus = {};
        });
      }

      // 使用nextTick确保DOM更新后再更新状态
      nextTick(() => {
        updateBusinessTypeConfigStatus();
        isUpdatingBusinessTypes.value = false;
      });
    } catch (error) {
      isUpdatingBusinessTypes.value = false;
    }
  },
  { deep: true, immediate: true },
);

// 监听资源池卡片的第一个业务类型变化
watch(
  () => resourcePoolCards.value.map((card) => [...card.businessTypes]),
  () => {
    // 遍历所有资源池卡片
    resourcePoolCards.value.forEach((card) => {
      // 过滤第二个业务类型，只保留在第一个业务类型中存在的选项
      card.businessTypesSecond = card.businessTypesSecond.filter((type) => card.businessTypes.includes(type));
    });
  },
  { deep: true },
);

// 获取资源池卡片的第二个业务类型选项，只显示第一个业务类型中已选择的选项（排除全单服务费）
const getPoolSecondBusinessTypeOptions = (cardIndex: number) => {
  const card = resourcePoolCards.value[cardIndex];
  if (!card || !Array.isArray(card.businessTypes) || card.businessTypes.length === 0) {
    return [];
  }

  // 返回第一个业务类型选择框中已选择的选项（排除全单服务费）
  return card.businessTypes
    .filter((type) => type !== '全单服务费')
    .map((type) => ({
      label: type,
      value: type,
    }));
};

// 获取过滤后的服务商列表（排除已经在排除列表中的服务商）
const getFilteredServiceProviderList = (cardIndex: number, currentRuleKey: string) => {
  const card = resourcePoolCards.value[cardIndex];
  if (!card || !Array.isArray(card.resourcePools)) {
    return serviceProviderList.value.filter((item) => item && item.code && item.code.trim());
  }

  // 如果当前规则是选中指定服务商，需要排除已经在排除列表中的服务商
  if (currentRuleKey === 'poolNameIncludeCondition') {
    // 找到所有排除指定供应商规则中已选择的服务商编码
    const excludedCodes = new Set<string>();

    card.resourcePools.forEach((pool) => {
      if (pool.matchRule === 'poolNameExcludeCondition' && Array.isArray(pool.matchValue)) {
        pool.matchValue.forEach((code) => {
          if (code && String(code).trim()) {
            excludedCodes.add(String(code).trim());
          }
        });
      }
    });

    // 过滤掉已排除的服务商
    return serviceProviderList.value.filter(
      (item) => item && item.code && item.code.trim() && !excludedCodes.has(item.code.trim()),
    );
  }

  // 如果当前规则是排除指定供应商，需要排除已经在包含列表中的服务商
  if (currentRuleKey === 'poolNameExcludeCondition') {
    // 找到所有选中指定服务商规则中已选择的服务商编码
    const includedCodes = new Set<string>();

    card.resourcePools.forEach((pool) => {
      if (pool.matchRule === 'poolNameIncludeCondition' && Array.isArray(pool.matchValue)) {
        pool.matchValue.forEach((code) => {
          if (code && String(code).trim()) {
            includedCodes.add(String(code).trim());
          }
        });
      }
    });

    // 过滤掉已包含的服务商
    return serviceProviderList.value.filter(
      (item) => item && item.code && item.code.trim() && !includedCodes.has(item.code.trim()),
    );
  }

  // 其他规则返回完整列表
  return serviceProviderList.value.filter((item) => item && item.code && item.code.trim());
};

// 监听资源池业务类型变化
watch(
  () =>
    JSON.stringify(
      resourcePoolCards.value.map((card) => ({
        id: card.id,
        businessTypes: [...card.businessTypes],
        applicationGroups: [...card.applicationGroups],
      })),
    ),
  () => {
    nextTick(() => {
      updateBusinessTypeConfigStatus();
    });
  },
  { deep: true },
);

// 监听资源组名称变化
watch(
  () => JSON.stringify(resourceGroups.value.map((group) => group.name)),
  () => {
    nextTick(() => {
      updateBusinessTypeConfigStatus();
    });
  },
  { deep: true },
);

// 强制更新配置状态
const forceUpdateStatus = () => {
  nextTick(() => {
    updateBusinessTypeConfigStatus();
  });
};

// 修改应用分组变更处理
const handleApplicationGroupChange = (cardIndex: number, groupIndex: number, value: string) => {
  // 更新应用分组值
  resourcePoolCards.value[cardIndex].applicationGroups[groupIndex] = value;

  // 强制更新配置状态
  forceUpdateStatus();
};

// 判断当前类型是否为集团外企业
const isExternalEnterprise = (type: string) => {
  // 如果没有类型选择，则返回false
  if (!type) return false;
  // 查找对应的规则元数据
  const ruleMetadata = roleRuleOptions.value.find((rule) => rule.ruleMetaKey === type);
  if (!ruleMetadata) return false;

  // 检查规则名称是否包含"集团外企业"关键词
  return ruleMetadata.ruleMetaName.includes('集团外企业');
};

// 获取已选择的企业名称列表（用于Tooltip显示）
const getSelectedEnterpriseNames = (selectedCodes: string[]) => {
  if (!Array.isArray(selectedCodes)) return '';

  const names = selectedCodes.map((code) => {
    const enterprise = enterpriseList.value.find((e) => e.code === code);
    return enterprise ? `${enterprise.name}(${enterprise.code})` : code;
  });

  return names.join('、');
};

// 获取已选择的部门名称列表（用于Tooltip显示）
const getSelectedDeptNames = (selectedCodes: string[]) => {
  if (!Array.isArray(selectedCodes)) return '';

  const names = selectedCodes.map((code) => {
    const dept = deptList.value.find((d) => d.code?.toString() === code);
    return dept ? `${dept.name}(${dept.code})` : code;
  });

  return names.join('、');
};

// 获取已选择的服务商名称列表（用于Tooltip显示）
const getSelectedServiceProviderNames = (selectedCodes: string[]) => {
  if (!Array.isArray(selectedCodes)) return '';

  const names = selectedCodes.map((code) => {
    const provider = serviceProviderList.value.find((p) => p.code === code);
    return provider ? `${provider.name}(${provider.code})` : code;
  });

  return names.join('、');
};

// 在isExternalEnterprise函数下面添加新的函数
const parseServiceProviderValue = (value: any): string[] => {
  // 如果是字符串，尝试解析成数组
  if (typeof value === 'string') {
    try {
      // 尝试解析JSON
      const parsedValue = JSON.parse(value);
      if (Array.isArray(parsedValue)) {
        // 过滤掉空字符串再返回
        return parsedValue.filter((item) => item && item.trim() !== '');
      }
      // 如果是逗号分隔的字符串，拆分为数组，过滤空字符串
      return value.split(',').filter((item: string) => item && item.trim() !== '');
    } catch (e) {
      // 解析失败，可能是单个值
      return value && value.trim() !== '' ? [value] : [];
    }
  }
  // 如果已经是数组，过滤空字符串再返回
  if (Array.isArray(value)) {
    return value.filter((item) => item && String(item).trim() !== '');
  }
  // 默认返回空数组
  return [];
};

// 处理按服务商类型匹配 - 获取选项文本
const getCheckboxLabelByValue = (ruleKey: string, value: string): string => {
  if (!ruleKey || !value || !matchValueOptions.value[ruleKey]) return value;

  const option = matchValueOptions.value[ruleKey].find(
    (opt: any) => opt.value === value || opt.value === String(value),
  );

  return option ? option.label : value;
};

// 将逗号分隔的值转换为对应的文本标签
const getCheckboxLabelsFromValues = (ruleKey: string, values: string[]): string => {
  if (!ruleKey || !values || values.length === 0) return '';

  const labels = values.map((value) => getCheckboxLabelByValue(ruleKey, value));
  return labels.filter(Boolean).join('、');
};

// 监听服务商选择变化，处理排除和包含的冲突
watch(
  () =>
    resourcePoolCards.value.map((card) =>
      card.resourcePools.map((pool) => ({
        matchRule: pool.matchRule,
        matchValue: pool.matchValue,
      })),
    ),
  () => {
    // 遍历所有资源池卡片
    resourcePoolCards.value.forEach((card) => {
      const excludedCodes = new Set<string>();
      const includedCodes = new Set<string>();

      // 收集所有排除和包含的服务商编码
      card.resourcePools.forEach((pool) => {
        if (pool.matchRule === 'poolNameExcludeCondition' && Array.isArray(pool.matchValue)) {
          pool.matchValue.forEach((code) => {
            if (code && String(code).trim()) {
              excludedCodes.add(String(code).trim());
            }
          });
        } else if (pool.matchRule === 'poolNameIncludeCondition' && Array.isArray(pool.matchValue)) {
          pool.matchValue.forEach((code) => {
            if (code && String(code).trim()) {
              includedCodes.add(String(code).trim());
            }
          });
        }
      });

      // 检查并清理冲突的选择
      card.resourcePools.forEach((pool) => {
        if (pool.matchRule === 'poolNameIncludeCondition' && Array.isArray(pool.matchValue)) {
          // 从包含列表中移除已排除的服务商
          const filteredValue = pool.matchValue.filter((code) => !excludedCodes.has(String(code).trim()));
          if (filteredValue.length !== pool.matchValue.length) {
            pool.matchValue = filteredValue as any;
          }
        } else if (pool.matchRule === 'poolNameExcludeCondition' && Array.isArray(pool.matchValue)) {
          // 从排除列表中移除已包含的服务商
          const filteredValue = pool.matchValue.filter((code) => !includedCodes.has(String(code).trim()));
          if (filteredValue.length !== pool.matchValue.length) {
            pool.matchValue = filteredValue as any;
          }
        }
      });
    });
  },
  { deep: true },
);
</script>

<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h-form
        ref="from"
        :model="processOrchestration"
        :rules="formRules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 17 }"
      >
        <h-form-item label="流程名称：" name="name">
          <h-input
            v-model:value="processOrchestration.name"
            placeholder="请输入流程名称"
            class="input-width"
            :disabled="isViewMode"
            :maxlength="200"
          />
        </h-form-item>

        <h-form-item label="流程简介：" name="description">
          <h-textarea
            v-model:value="processOrchestration.description"
            placeholder="请输入流程简介"
            :auto-size="{ minRows: 3, maxRows: 6 }"
            class="input-width"
            :disabled="isViewMode"
            :maxlength="200"
          />
        </h-form-item>

        <h-form-item label="订单编号前缀：" name="prefix">
          <h-input
            v-model:value="processOrchestration.prefix"
            placeholder="请输入订单编号前缀"
            class="input-width"
            :disabled="isViewMode"
          />
        </h-form-item>

        <h-form-item name="isHotelDemandSubmittable">
          <template #label>
            是否可提报多酒店需求
            <h-tooltip title="需求提报时酒店需求是否可以提报多个" placement="top">
              <QuestionCircleOutlined style="margin-left: 4px; color: #1890ff; cursor: pointer;" />
            </h-tooltip>
          </template>
          <h-switch v-model:checked="processOrchestration.isHotelDemandSubmittable" :disabled="isViewMode" />
          <span style="margin-left: 8px; color: #666">
            {{ processOrchestration.isHotelDemandSubmittable ? '已开启' : '已关闭' }}
          </span>
        </h-form-item>

        <h-form-item name="supportInternational">
          <template #label>
            是否支持国际会议提报
            <h-tooltip title="需求提报时是否可以选择国际会议" placement="top">
              <QuestionCircleOutlined style="margin-left: 4px; color: #1890ff; cursor: pointer;" />
            </h-tooltip>
          </template>
          <h-switch v-model:checked="processOrchestration.supportInternational" :disabled="isViewMode" />
          <span style="margin-left: 8px; color: #666">
            {{ processOrchestration.supportInternational ? '已开启' : '已关闭' }}
          </span>
        </h-form-item>

        <h-form-item name="platformFeeRate">
          <template #label>
            用户平台服务费费率(%)：
            <h-tooltip title="按照配置比例用户付款给平台的金额" placement="top">
              <QuestionCircleOutlined style="margin-left: 4px; color: #1890ff; cursor: pointer;" />
            </h-tooltip>
          </template>
          <h-input-number
            v-model:value="processOrchestration.platformFeeRate"
            :min="0"
            :max="100"
            :precision="2"
            placeholder="请输入，0为不收取"
            class="match-rule-select"
            addonAfter="%"
            :disabled="isViewMode"
          />
        </h-form-item>

        <h-form-item label="业务类型：" name="items">
          <h-checkbox-group v-model:value="processOrchestration.items" :disabled="isViewMode">
            <h-checkbox v-for="option in businessTypeOptions" :key="option.value" :value="option.label">
              {{ option.label }}
            </h-checkbox>
          </h-checkbox-group>
        </h-form-item>

        <!-- 适用用户选择部分 -->
        <h-form-item name="users">
          <template #label>
            <span class="required-label">适用用户</span>
            <h-tooltip title="当前流程会按照此配置限制可以访问该流程的用户" placement="top">
              <QuestionCircleOutlined style="margin-left: 4px; color: #1890ff; cursor: pointer;" />
            </h-tooltip>
          </template>
          <div class="user-select-area">
            <div v-for="(item, index) in selectedUsers" :key="index" class="user-select-row">
              <h-select
                v-model:value="item.type"
                class="user-type-select"
                placeholder="请选择"
                @change="(value) => handleUserTypeChange(value, index)"
                :disabled="isViewMode"
                allow-clear
              >
                <h-select-option
                  v-for="option in roleRuleOptions"
                  :key="option.ruleMetaKey"
                  :value="option.ruleMetaKey"
                  class="user-option-text"
                >
                  {{ option.ruleMetaName }}
                </h-select-option>
              </h-select>

              <!-- 根据类型动态显示不同的选择控件 -->
              <template v-if="shouldUseUserSelect(item.type)">
                <!-- 如果是集团内部门类型，显示部门选择器 -->
                <template
                  v-if="
                    item.type &&
                    roleRuleOptions.find((rule) => rule.ruleMetaKey === item.type)?.ruleMetaName.includes('集团内部门')
                  "
                >
                  <div class="dept-select-container">
                    <h-tooltip
                      :title="getSelectedDeptNames(item.user)"
                      placement="top"
                      :visible="Array.isArray(item.user) && item.user.length > 3 ? undefined : false"
                    >
                      <h-select
                        v-model:value="item.user"
                        style="width: 100%"
                        placeholder="请输入部门名称搜索"
                        :filter-option="false"
                        show-search
                        mode="multiple"
                        @search="(value) => fetchDept(value)"
                        :maxTagCount="3"
                        :maxTagPlaceholder="(omittedValues: any) => `+${omittedValues.length}`"
                        :disabled="isViewMode"
                        allow-clear
                      >
                        <h-select-option
                          v-for="(dept, deptIndex) in deptList"
                          :key="deptIndex"
                          :value="dept.code?.toString()"
                        >
                          {{ dept.name }}({{ dept.code }})
                        </h-select-option>
                      </h-select>
                    </h-tooltip>
                  </div>
                </template>
                <!-- 指定集团内员工工号类型使用UserSelect组件 -->
                <template
                  v-else-if="
                    item.type &&
                    roleRuleOptions
                      .find((rule) => rule.ruleMetaKey === item.type)
                      ?.ruleMetaName.includes('指定集团内员工工号')
                  "
                >
                  <div class="user-select-container">
                    <UserSelectPerson
                      :value="getUsernames(item.user, index)"
                      :params="userSelectParams"
                      placeholder="请选择用户"
                      @change="(value) => handleUserChange(value, index)"
                      multiple
                      :disabled="isViewMode"
                    />
                  </div>
                </template>
              </template>
              <!-- 如果是集团外企业类型，显示企业选择器 -->
              <template v-else-if="isExternalEnterprise(item.type)">
                <div class="enterprise-select-container">
                  <h-tooltip
                    :title="getSelectedEnterpriseNames(item.user)"
                    placement="top"
                    :visible="Array.isArray(item.user) && item.user.length > 3 ? undefined : false"
                  >
                    <h-select
                      v-model:value="item.user"
                      style="width: 100%"
                      placeholder="请选择集团外企业"
                      show-search
                      mode="multiple"
                      :filter-option="false"
                      @search="searchEnterprise"
                      :maxTagCount="4"
                      :maxTagPlaceholder="(omittedValues: any) => `+${omittedValues.length}`"
                      :disabled="isViewMode"
                      allow-clear
                    >
                      <h-select-option
                        v-for="(enterprise, enterpriseIndex) in enterpriseList"
                        :key="enterpriseIndex"
                        :value="enterprise.code"
                      >
                        {{ enterprise.name }}({{ enterprise.code }})
                      </h-select-option>
                    </h-select>
                  </h-tooltip>
                </div>
              </template>
              <template v-else>
                <h-input v-model:value="item.user" class="user-input" placeholder="请输入" :disabled="isViewMode" />
              </template>
              <template
                v-if="
                  item.type &&
                  roleRuleOptions
                    .find((rule) => rule.ruleMetaKey === item.type)
                    ?.ruleMetaName.includes('集团内用户均可')
                "
              >
              </template>
              <h-button
                v-if="selectedUsers.length > 1 && !isViewMode"
                type="link"
                @click="removeUserSelection(index)"
                class="remove-btn"
                >移除</h-button
              >

              <h-button
                v-if="index === selectedUsers.length - 1 && !isViewMode"
                type="link"
                @click="addUserSelection"
                class="add-btn"
                >添加</h-button
              >
            </div>
          </div>
        </h-form-item>

        <h-form-item label="资源组：" name="resourceGroup">
          <div class="resource-group-area">
            <div v-for="(group, index) in resourceGroups" :key="index" class="resource-group-item">
              <h-card class="resource-card" :bordered="true">
                <div class="resource-header">
                  <h-button
                    v-if="resourceGroups.length > 1 && !isViewMode"
                    type="link"
                    class="remove-icon-btn"
                    @click="removeResourceGroup(index)"
                  >
                    <svg
                      viewBox="64 64 896 896"
                      focusable="false"
                      class="delete-icon"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        d="M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.1 0 62.3-26.9 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"
                      ></path>
                    </svg>
                  </h-button>
                </div>

                <div class="resource-content">
                  <div class="resource-row">
                    <span class="resource-label required-label">资源小组名称：</span>
                    <h-input
                      v-model:value="group.name"
                      placeholder="请输入，例如 分组01"
                      class="match-rule-select"
                      :disabled="isViewMode"
                    />
                  </div>



                  <div class="resource-row">
                    <span class="resource-label">业务类型配置状态：</span>
                    <div class="status-list">
                      <template v-if="Object.keys(group.configStatus).length > 0">
                        <div v-for="(status, type) in group.configStatus" :key="type" class="status-item">
                          <span>{{ type }}</span>
                          <span :class="getStatusClass(status)">({{ status }})</span>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </h-card>
            </div>

            <div class="add-resource-btn" v-if="!isViewMode">
              <h-button type="dashed" @click="addResourceGroup" block>
                <span>添加资源组</span>
              </h-button>
            </div>
          </div>
        </h-form-item>

        <!-- 资源池组件 -->
        <h-form-item label="资源池：" name="resourcePool">
          <div v-for="(card, cardIndex) in resourcePoolCards" :key="card.id" class="resource-pool-area">
            <h-card class="resource-card" :bordered="true">
              <div class="resource-header">
                <h-button
                  v-if="resourcePoolCards.length > 1 && !isViewMode"
                  type="link"
                  class="remove-icon-btn"
                  @click="() => removeResourcePoolCard(cardIndex)"
                >
                  <svg
                    viewBox="64 64 896 896"
                    focusable="false"
                    class="delete-icon"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      d="M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.1 0 62.3-26.9 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"
                    ></path>
                  </svg>
                </h-button>
              </div>
              <div class="resource-content">
                <div class="resource-row">
                  <span class="resource-label required-label">资源池名称：</span>
                  <h-input
                    v-model:value="card.name"
                    placeholder="请输入资源池名称"
                    class="match-rule-select"
                    :disabled="isViewMode"
                  />
                </div>

                <div class="resource-row">
                  <span class="resource-label required-label">
                    服务商匹配规则：
                    <h-tooltip title="按照选择的策略筛选出数据库中所有未删除的服务商（包括冻结的和试用期的）" placement="top">
                      <QuestionCircleOutlined style="margin-left: 4px; color: #1890ff; cursor: pointer;" />
                    </h-tooltip>
                  </span>
                  <div class="resource-rule-container">
                    <div v-for="(pool, poolIndex) in card.resourcePools" :key="poolIndex" class="resource-rule-row">
                      <h-select
                        v-model:value="pool.matchRule"
                        class="match-rule-select"
                        placeholder="请选择"
                        @change="(value) => handleRuleChange(value, cardIndex, poolIndex)"
                        :disabled="isViewMode"
                        allow-clear
                      >
                        <h-select-option
                          v-for="option in matchRuleOptions"
                          :key="option.configs.key"
                          :value="option.configs.key"
                          >{{ option.configs.description }}</h-select-option
                        >
                      </h-select>

                      <!-- 根据匹配值类型动态渲染不同的控件 -->
                      <h-select
                        v-if="pool.matchValueType === 'CHECKBOX'"
                        v-model:value="pool.selectedCheckboxValues"
                        class="match-select"
                        placeholder="请选择"
                        @change="
                          (value) => {
                            console.log('CHECKBOX值已更改:', value);
                            // 转换为数组形式保存，以保持代码一致性
                            pool.selectedCheckboxValues = Array.isArray(value) ? value : [value];
                          }
                        "
                        :disabled="isViewMode"
                        allowClear
                      >
                        <h-select-option
                          v-for="option in matchValueOptions[pool.matchRule] || []"
                          :key="option.value"
                          :value="option.value"
                          allowClear
                        >
                          {{ option.label }}
                        </h-select-option>
                      </h-select>

                      <h-tooltip
                        v-else-if="pool.matchValueType === 'SELECT'"
                        :title="getSelectedServiceProviderNames(pool.matchValue)"
                        placement="top"
                        :visible="Array.isArray(pool.matchValue) && pool.matchValue.length > 1 ? undefined : false"
                      >
                        <h-select
                          v-model:value="pool.matchValue"
                          class="match-select"
                          placeholder="请选择"
                          mode="multiple"
                          @search="fetchServiceProvider"
                          show-search
                          :filter-option="false"
                          :maxTagCount="1"
                          :maxTagPlaceholder="(omittedValues: any) => `+${omittedValues.length}`"
                          :disabled="isViewMode"
                          allowClear
                        >
                          <h-select-option
                            v-for="option in getFilteredServiceProviderList(cardIndex, pool.matchRule)"
                            :key="option.code"
                            :value="option.code"
                          >
                            {{ option.name }}
                          </h-select-option>
                        </h-select>
                      </h-tooltip>

                      <div class="action-buttons-container">
                        <h-button type="link" @click="viewMatchResult(cardIndex, poolIndex)" class="action-btn view-btn"
                          >查看匹配结果</h-button
                        >
                        <h-button
                          v-if="!isViewMode"
                          type="link"
                          @click="removeResourcePool(poolIndex)"
                          class="action-btn remove-btn"
                          >移除</h-button
                        >
                        <h-button
                          v-if="poolIndex === card.resourcePools.length - 1 && !isViewMode"
                          type="link"
                          @click="() => addResourcePool(cardIndex)"
                          class="action-btn add-btn"
                          >添加</h-button
                        >
                      </div>
                    </div>
                  </div>
                </div>

                <div class="resource-row">
                  <span class="resource-label"></span>
                  <div class="rule-explanation">
                    <h-button type="link" @click="viewAllMatchResults(cardIndex)" class="view-all-link"
                      >查看全部匹配结果</h-button
                    >
                    <p class="explanation-text">
                      说明：如服务商类型匹配"新"添加的服务商，如包含此类型（且不在排除列表内），则自动被添加至资源池
                    </p>
                  </div>
                </div>

                <div class="resource-row">
                  <span class="resource-label required-label">
                    是否底价服务商：
                    <h-tooltip title="设置该资源池是否为底价服务商" placement="top">
                      <QuestionCircleOutlined style="margin-left: 4px; color: #1890ff; cursor: pointer;" />
                    </h-tooltip>
                  </span>
                  <h-select
                    v-model:value="card.isBasePriceMerchant"
                    class="auto-commit-select"
                    placeholder="请选择是否底价服务商"
                    :disabled="isViewMode"
                    allow-clear
                  >
                    <h-select-option :value="true">是</h-select-option>
                    <h-select-option :value="false">否</h-select-option>
                  </h-select>
                </div>

                <div class="resource-row">
                  <span class="resource-label required-label">业务类型：</span>
                  <h-checkbox-group v-model:value="card.businessTypes" :disabled="isViewMode">
                    <h-checkbox
                      v-for="option in filteredPoolFirstBusinessTypeOptions"
                      :key="option.value"
                      :value="option.label"
                    >
                      {{ option.label }}
                    </h-checkbox>
                  </h-checkbox-group>
                </div>
                <div class="resource-row">
                  <span class="resource-label required-label">
                    收款方：
                    <h-tooltip title="最终方案价格乘该比例后的金额付款给收款方" placement="top">
                      <QuestionCircleOutlined style="margin-left: 4px; color: #1890ff; cursor: pointer;" />
                    </h-tooltip>
                  </span>
                  <h-select
                    v-model:value="card.payee"
                    class="payee-select"
                    placeholder="请选择收款方（必填）"
                    :disabled="isViewMode"
                    allow-clear
                  >
                    <h-select-option v-for="option in payeeOptions" :key="option.value" :value="option.value">
                      {{ option.label }}
                    </h-select-option>
                  </h-select>
                </div>

                <div class="resource-row">
                  <span class="resource-label required-label">
                    付款比例：
                    <h-tooltip title="最终方案金额乘付款比例后指定方收取计算后的金额" placement="top">
                      <QuestionCircleOutlined style="margin-left: 4px; color: #1890ff; cursor: pointer;" />
                    </h-tooltip>
                  </span>
                  <h-input-number
                    v-model:value="card.paymentRate"
                    :min="0"
                    :max="100"
                    :precision="2"
                    placeholder="请输入付款比例（必填，可填0）"
                    class="payment-ratio-input"
                    addonAfter="%"
                    :disabled="isViewMode"
                  />
                </div>

                <div class="resource-row">
                  <span class="resource-label required-label">
                    是否方案自动提报：
                    <h-tooltip title="方案按照用户提报的需求自动生成提报" placement="top">
                      <QuestionCircleOutlined style="margin-left: 4px; color: #1890ff; cursor: pointer;" />
                    </h-tooltip>
                  </span>
                  <h-select
                    v-model:value="card.schemeAutoCommit"
                    class="auto-commit-select"
                    placeholder="请选择是否方案自动提报"
                    :disabled="isViewMode"
                    allow-clear
                  >
                    <h-select-option :value="true">是</h-select-option>
                    <h-select-option :value="false">否</h-select-option>
                  </h-select>
                </div>

                <div class="resource-row">
                  <span class="resource-label required-label">
                    是否自动竞价：
                    <h-tooltip title="竞价节点按照方案价格自动提报竞价数据" placement="top">
                      <QuestionCircleOutlined style="margin-left: 4px; color: #1890ff; cursor: pointer;" />
                    </h-tooltip>
                  </span>
                  <h-select
                    v-model:value="card.bidAutoPush"
                    class="auto-bid-select"
                    placeholder="请选择是否自动竞价"
                    :disabled="isViewMode"
                    allow-clear
                  >
                    <h-select-option :value="true">是</h-select-option>
                    <h-select-option :value="false">否</h-select-option>
                  </h-select>
                </div>
                <div class="resource-row" v-if="hasPoolFullServiceFee(cardIndex)">
                  <span class="resource-label required-label">
                    全单服务费费率(%)：
                    <h-tooltip title="服务商按照配置比例收取用户的金额" placement="top">
                      <QuestionCircleOutlined style="margin-left: 4px; color: #1890ff; cursor: pointer;" />
                    </h-tooltip>
                  </span>
                  <h-input-number
                    v-model:value="card.serviceFee"
                    :min="0"
                    :max="100"
                    :precision="2"
                    placeholder="请输入，0为不收取"
                    class="service-fee-input"
                    :disabled="isViewMode"
                    addonAfter="%"
                  />
                </div>

                <div class="resource-row" v-if="hasPoolFullServiceFee(cardIndex)">
                  <span class="resource-label">
                    全单服务费说明：
                    <h-tooltip title="对全单服务费的补充说明信息" placement="top">
                      <QuestionCircleOutlined style="margin-left: 4px; color: #1890ff; cursor: pointer;" />
                    </h-tooltip>
                  </span>
                  <h-textarea
                    v-model:value="card.fullServiceRemark"
                    placeholder="请输入全单服务费说明"
                    :auto-size="{ minRows: 2, maxRows: 4 }"
                    class="service-fee-input"
                    :disabled="isViewMode"
                    :maxlength="500"
                  />
                </div>

                <div class="resource-row" v-if="hasPoolFullServiceFee(cardIndex)">
                  <span class="resource-label">
                    业务类型：
                    <h-tooltip title="全单服务费业务类型：指定业务类型的方案金额会按照全单服务费费率计算全单服务费" placement="top">
                      <QuestionCircleOutlined style="margin-left: 4px; color: #1890ff; cursor: pointer;" />
                    </h-tooltip>
                  </span>
                  <h-checkbox-group v-model:value="card.businessTypesSecond" :disabled="isViewMode">
                    <h-checkbox
                      v-for="option in getPoolSecondBusinessTypeOptions(cardIndex)"
                      :key="option.value"
                      :value="option.label"
                    >
                      {{ option.label }}
                    </h-checkbox>
                  </h-checkbox-group>
                </div>

                <div class="resource-row">
                  <span class="resource-label required-label">应用至分组：</span>
                  <div class="application-group-wrapper">
                    <div
                      v-for="(group, groupIndex) in card.applicationGroups"
                      :key="groupIndex"
                      class="application-group-row"
                    >
                      <h-select
                        v-model:value="card.applicationGroups[groupIndex]"
                        class="group-select"
                        placeholder="请选择"
                        @change="(value) => handleApplicationGroupChange(cardIndex, groupIndex, value)"
                        :disabled="isViewMode"
                        allow-clear
                      >
                        <h-select-option
                          v-for="option in resourceGroupOptions"
                          :key="option.value"
                          :value="option.value"
                          >{{ option.label }}</h-select-option
                        >
                      </h-select>
                      <h-button
                        v-if="!isViewMode"
                        type="link"
                        class="action-btn remove-btn"
                        @click="removeApplicationGroup(cardIndex, groupIndex)"
                        >移除</h-button
                      >
                      <h-button
                        v-if="groupIndex === card.applicationGroups.length - 1 && !isViewMode"
                        type="link"
                        class="action-btn add-btn"
                        @click="addApplicationGroup(cardIndex)"
                        >添加</h-button
                      >
                    </div>
                  </div>
                </div>
              </div>
            </h-card>
          </div>

          <div class="add-resource-btn" v-if="!isViewMode">
            <h-button type="dashed" @click="addResourcePoolCard" block>
              <span>添加资源池</span>
            </h-button>
          </div>
        </h-form-item>

        <!-- 富文本框 -->
        <h-form-item name="processNotice">
          <template #label>
            流程须知
            <h-tooltip title="配置后会展示在对应流程门户中" placement="top">
              <QuestionCircleOutlined style="margin-left: 4px; color: #1890ff; cursor: pointer;" />
            </h-tooltip>
          </template>
          <!-- 查看模式使用div展示富文本内容 -->
          <div
            v-if="isViewMode"
            class="process-notice-view input-width"
            v-html="(processOrchestration as any).processNotice || '暂无流程须知'"
          ></div>
          <!-- 编辑模式使用富文本编辑器 -->
          <Editor
            v-else
            height="350px"
            :modelValue="(processOrchestration as any).processNotice || ''"
            @change="onEditorChange"
            class="input-width"
            style="z-index: 20"
            uploadUrl="/upload"
          />
        </h-form-item>

        <!-- 修改内容（仅编辑时显示） -->
        <h-form-item v-if="isEditMode" label="修改内容：" name="modifyContent">
          <h-textarea
            v-model:value="processOrchestration.modifyContent"
            placeholder="请输入修改内容"
            :auto-size="{ minRows: 3, maxRows: 5 }"
            class="input-width"
            :disabled="isViewMode"
          />
        </h-form-item>
      </h-form>
    </div>

    <div class="footer-container">
      <h-button v-if="!isViewMode && !isEditMode" @click="openImportModal" style="margin-right: 10px">
        导入
      </h-button>
      <h-button v-if="!isViewMode && !isEditMode" @click="manualSave" style="margin-right: 10px">
        暂存
      </h-button>
      <!-- 自动保存倒计时显示 -->
      <div v-if="!isViewMode && !isEditMode" class="auto-save-info" style="margin-right: 15px; color: #666; font-size: 12px;">
        {{ countdownTime === 0 ? '已自动保存' : `${countdownTime}s后自动保存` }}
      </div>
      <h-button type="primary" html-type="submit" class="submit-button" @click="handleOk">
        {{ isViewMode ? '下一步' : '下一步' }}
      </h-button>
    </div>

    <h-drawer
      v-model:visible="drawerVisible"
      :title="drawerTitle"
      width="50%"
      :footer="null"
      :destroy-on-close="true"
      :mask="true"
      :maskClosable="true"
      :closable="true"
      placement="right"
      :class="{ 'drawer-closing': isClosing }"
      :body-style="drawerBodyStyle"
      @close="handleDrawerClose"
    >
      <div class="drawer-content">
        <h-empty v-if="!matchResults.length && !drawerLoading" description="暂无匹配结果" />
        <h-table
          v-else
          :columns="matchResultColumns"
          :data-source="matchResults"
          :pagination="false"
          :loading="drawerLoading"
          :row-key="(record) => record.id"
          :scroll="{ y: '400px' }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'merchantName'">
              {{ record.merchantName }}
            </template>
            <template v-else-if="column.key === 'merchantCode'">
              {{ record.merchantCode }}
            </template>
            <template v-else-if="column.key === 'merchantType'">
              {{ MerchantTypeMap[record.merchantType as MerchantTypeEnum] || '-' }}
            </template>
            <template v-else-if="column.key === 'score'">
              {{ record.score }}
            </template>
          </template>
        </h-table>
        <!-- 独立分页组件 -->
        <div class="pagination-wrapper" v-if="matchResults.length > 0">
          <h-pagination
            v-model:current="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :show-size-changer="pagination.showSizeChanger"
            :show-quick-jumper="pagination.showQuickJumper"
            @change="(page, pageSize) => handlePageChange(page, pageSize)"
            @showSizeChange="(current, size) => handlePageChange(current, size)"
          />
        </div>
      </div>
    </h-drawer>

    <!-- 导入数据弹窗 -->
    <h-modal
      v-model:open="importModalVisible"
      title="导入流程数据"
      width="70%"
      :confirmLoading="importLoading"
      @ok="handleImport"
      @cancel="closeImportModal"
      okText="导入"
      cancelText="取消"
    >
      <div class="import-data-container">
        <div style="margin-bottom: 16px; color: #666;">
          请粘贴从导出功能复制的数据：
        </div>
        <h-textarea
          v-model:value="importData"
          placeholder="请粘贴从导出功能复制的JSON数据..."
          :auto-size="{ minRows: 10, maxRows: 20 }"
          style="font-family: Monaco, Menlo, 'Ubuntu Mono', monospace; font-size: 12px;"
        />
        <div style="margin-top: 8px; color: #999; font-size: 12px;">
          提示：数据必须是有效的JSON格式
        </div>
      </div>
    </h-modal>

    <!-- 缓存数据恢复弹窗 -->
    <h-modal
      v-model:open="cacheRestoreModalVisible"
      title="发现暂存数据"
      width="500px"
      :closable="false"
      :maskClosable="false"
    >
      <div style="padding: 20px 0;">
        <p style="margin-bottom: 16px; color: #666;">
          检测到您之前有未完成的表单数据，是否需要恢复？
        </p>
        <p style="margin-bottom: 0; color: #999; font-size: 12px;">
          选择"恢复数据"将加载之前填写的内容，选择"重新开始"将清除暂存数据。
        </p>
      </div>
      <template #footer>
        <div style="text-align: right;">
          <h-button @click="deleteCacheData" style="margin-right: 8px;">
            重新开始
          </h-button>
          <h-button type="primary" @click="restoreCacheData">
            恢复数据
          </h-button>
        </div>
      </template>
    </h-modal>
  </div>
</template>

<style lang="less" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  position: relative;
}

.content-wrapper {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  margin-bottom: 60px;
  /* 给底部按钮留出空间 */
}

.footer-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 24px;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.auto-save-info {
  display: flex;
  align-items: center;
}

.footer-actions {
  height: 60px;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 12px 24px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  z-index: 10;

  button {
    margin: 0 8px;
    min-width: 80px;
  }
}

.submit-button {
  width: 120px;
  height: 32px;
  border-radius: 4px;
}

.user-select-area {
  width: 100%;
  max-width: 1100px;
}

.resource-group-area {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 10px;
  min-height: 50px;
  width: 100%;
  max-width: 1100px;
}

.user-select-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  width: 100%;
}

.user-action-row {
  margin-top: 5px;
}

.remove-btn {
  color: #ff4d4f;
}

.remove-icon-btn {
  color: #8c8c8c;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.3s;

  &:hover {
    background-color: #f5f5f5;
    color: #595959;
  }
}

.delete-icon {
  width: 16px;
  height: 16px;
  display: block;
}

.add-btn {
  color: #52c41a;
  margin-left: 5px;
}

.input-width {
  max-width: 1100px;
  width: 100%;
}

.resource-group-area {
  width: 100%;
  max-width: 1100px;
}

.resource-group-item {
  margin-bottom: 16px;
}

.resource-card {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background-color: #fafafa;
}

.resource-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16px;
}

.resource-title {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.resource-content {
  padding: 0 8px;
}

.resource-row {
  display: flex;
  margin-bottom: 16px;
}

.resource-label {
  min-width: 160px;
  display: inline-block;
  text-align: right;
  padding-right: 12px;
}

.required-label::before {
  content: '* ';
  color: #ff4d4f;
}

.resource-input {
  width: 320px;
}

.status-row {
  align-items: flex-start;
}

.status-list {
  display: flex;
  flex-wrap: wrap;
}

.status-item {
  margin-right: 16px;
  margin-bottom: 8px;
}

.add-resource-btn {
  margin-top: 16px;
  width: 100%;
  max-width: 1100px;
}

.resource-pool-area {
  width: 100%;
  max-width: 1100px;
  margin-bottom: 16px;
  position: relative;
}

.resource-rule-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.resource-rule-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
  min-width: 100%;
}

.resource-rule-row:last-child {
  margin-bottom: 0;
}

.rule-explanation {
  margin-top: -10px;
  margin-bottom: 10px;
  width: 100%;
}

.resource-pool-card {
  width: 100%;
}

.application-group-wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.application-group-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.application-group-row:last-child {
  margin-bottom: 0;
}

.pool-card-title {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 16px;
  display: inline-block;
  min-width: 120px;
}

.pool-explanation {
  margin-bottom: 16px;
}

.explanation-text {
  margin-top: 4px;
  color: #666;
  font-size: 12px;
}

.business-types-section {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.business-types-section span {
  min-width: 120px;
  display: inline-block;
}

.service-fee-section {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.service-fee-section span {
  min-width: 120px;
  display: inline-block;
}

.application-group-section {
  margin-bottom: 16px;
}

.application-group-section > span {
  min-width: 120px;
  display: inline-block;
}

.pool-actions {
  margin-top: 16px;
}

.pool-action-btn {
  margin-left: 8px;
}

.remove-pool-btn {
  color: #ff4d4f;
}

.add-pool-btn {
  color: #52c41a;
}

.pool-card-actions {
  margin-top: 16px;
  border-top: 1px dashed #f0f0f0;
  padding-top: 16px;
  text-align: right;
}

.add-pool-card-action {
  margin-top: 8px;
  margin-bottom: 24px;
}

.view-btn {
  color: #1890ff;
}

.view-all-link {
  color: #1890ff;
  padding-left: 0;
}

.drawer-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px 0;
  overflow: hidden;
}

:deep(.ant-table-wrapper) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.ant-table-container) {
  flex: 1;
  overflow: auto;
}

:deep(.ant-table-pagination) {
  margin: 16px 0 0 0;
  padding: 0;
}

.pagination-wrapper {
  margin-top: 16px;
  text-align: center;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
}

.drawer-footer {
  margin-top: 20px;
  text-align: right;
  padding: 12px 0;
}

:deep(.drawer-closing) {
  transition: all 0.3s ease-in-out;

  .ant-drawer-content-wrapper {
    transition: transform 0.3s ease-in-out !important;
  }

  .drawer-content {
    opacity: 0;
    transition: opacity 0.2s ease-out;
  }
}

:deep(.ant-form-item-required::before) {
  display: none !important;
}

:deep(.required-label::before) {
  display: none !important;
}

.user-type-select {
  width: 160px;
  margin-right: 10px;
  min-width: 160px;
  max-width: 160px;
}

.user-option-text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.dept-select-container,
.user-select-container,
.enterprise-select-container {
  flex: 1;
  max-width: calc(100% - 160px - 160px);
  margin-right: 10px;
}

.user-input {
  flex: 1;
  max-width: 100%;
  margin-right: 10px;
}

.match-rule-select {
  width: 280px;
  margin-right: 10px;
  flex-shrink: 0;
}

.match-select {
  width: 280px;
  margin-right: 10px;
  flex-shrink: 0;
}

.checkbox-group-wrapper {
  display: inline-flex;
  flex-wrap: wrap;
  margin-right: 10px;
}

.match-input {
  width: 180px;
  margin-right: 10px;
}

.service-fee-input {
  width: 280px;
}

.group-select {
  width: 280px;
  margin-right: 10px;
}

.payee-select {
  width: 280px;
}

.payment-ratio-input {
  width: 280px;
}

.auto-commit-select {
  width: 280px;
}

.auto-bid-select {
  width: 280px;
}

// 状态颜色类
.status-success {
  color: #52c41a;
}

.status-error {
  color: #ff4d4f;
}

.status-warning {
  color: #faad14;
  font-style: italic;
  margin-top: 5px;
}

.status-default {
  color: #999;
}

// 按钮容器样式
.action-buttons-container {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-left: 10px;
  white-space: nowrap;
}

.action-btn {
  white-space: nowrap;
  flex-shrink: 0;
  margin-left: 8px;
}

.action-buttons-container .action-btn:first-child {
  margin-left: 0;
}

// 仅影响当前页面
.page-container :deep(.ant-select-selection-overflow-item-rest .ant-select-selection-item) {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
  border: none !important;
}

/* 流程须知查看模式样式 */
.process-notice-view {
  min-height: 100px;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  max-height: 400px;
  overflow-y: auto;
}

.process-notice-view p {
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.process-notice-view:empty::before {
  content: '暂无流程须知';
  color: #999;
  font-style: italic;
}

/* 导入数据弹窗样式 */
.import-data-container {
  max-height: 600px;
}
</style>

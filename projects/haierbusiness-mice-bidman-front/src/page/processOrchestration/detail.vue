<!-- 流程编排详情查看 -->
<script setup lang="ts">
import {
  Button as hButton,
  Tag as hTag,
  Spin as hSpin,
  Card as hCard,
  Timeline as hTimeline,
  TimelineItem as hTimelineItem,
  Empty as hEmpty,
} from 'ant-design-vue';
import { processOrchestrationApi } from '@haierbusiness-front/apis';
import {
  IProcessOrchestration,
  ProcessOrchestrationModelStatusEnum,
  ProcessOrchestrationServiceTypeEnum,
} from '@haierbusiness-front/common-libs';
import { ref, onMounted, reactive, h } from 'vue';
import { useRoute, useRouter } from 'vue-router';

// 路由相关
const route = useRoute();
const router = useRouter();

// 加载状态
const loading = ref(false);

// 流程详情数据
const processDetail = ref<any>(null);

// 分组节点数据
const groupedNodes = ref<any[]>([]);

// 获取流程详情
const fetchProcessDetail = async () => {
  const id = Number(route.query.id);
  if (!id) {
    return;
  }

  loading.value = true;
  try {
    const result = await processOrchestrationApi.get(id);
    processDetail.value = result;
    console.log('流程详情数据:', processDetail.value);

    // 处理节点分组
    if (processDetail.value && processDetail.value.nodes) {
      groupedNodes.value = processDetail.value.nodes;
    }
  } catch (error) {
    console.error('获取流程详情失败:', error);
  } finally {
    loading.value = false;
  }
};

// 使用枚举提供的方法获取状态文本
const getStatusText = (status?: number) => {
  return status !== undefined ? ProcessOrchestrationModelStatusEnum.getStatusText(status) : '';
};

// 使用枚举提供的方法获取状态样式类
const getStatusClass = (status?: number) => {
  return status !== undefined ? ProcessOrchestrationModelStatusEnum.getStatusClass(status) : '';
};

// 获取业务类型文本描述
const getBusinessTypeText = (type?: number) => {
  return type !== undefined ? ProcessOrchestrationServiceTypeEnum.getTypeText(type) : '';
};

// 获取阶段颜色
const getStageColor = (stage: string) => {
  const stageColors: Record<string, string> = {
    需求: '#1890ff',
    方案: '#52c41a',
    竞价: '#fa8c16',
    执行: '#722ed1',
    结算: '#eb2f96',
    '': '#d9d9d9',
  };
  return stageColors[stage] || '#d9d9d9';
};

// 获取阶段图标
const getStageIcon = (stage: string) => {
  const stageIcons: Record<string, string> = {
    需求: '📝',
    方案: '📋',
    竞价: '💰',
    执行: '⚡',
    结算: '💳',
    '': '🔚',
  };
  return stageIcons[stage] || '📄';
};

// 获取配置项的显示文本
const getDisplayText = (reverseDisplayParam: string) => {
  try {
    const parsed = JSON.parse(reverseDisplayParam);
    return parsed.displayText || parsed.selectedLabel || parsed.value || reverseDisplayParam;
  } catch (e) {
    return reverseDisplayParam;
  }
};

onMounted(() => {
  fetchProcessDetail();
});
</script>

<template>
  <div class="process-detail-container">
    <h-spin :spinning="loading">
      <div class="header">
        <h2>流程编排节点详情</h2>
      </div>

      <div v-if="processDetail">
        <!-- 流程节点内容 -->
        <div class="nodes-container">
          <h-timeline mode="alternate" v-if="processDetail.nodes && processDetail.nodes.length">
            <h-timeline-item
              v-for="(node, index) in processDetail.nodes"
              :key="index"
              :color="node.stage ? '#1890ff' : '#d9d9d9'"
            >
              <h-card :bordered="false" class="node-card">
                <template #title>
                  <div class="node-title">
                    <span class="node-seq">{{ node.seq }}</span>
                    <span class="node-name">{{ node.nodeName }}</span>
                  </div>
                </template>
                <div class="node-content">
                  <p><strong>角色：</strong>{{ node.role || '-' }}</p>
                  <p><strong>描述：</strong>{{ node.description || '-' }}</p>
                  <div v-if="node.states && node.states.length">
                    <strong>状态：</strong>
                    <div class="node-states">
                      <h-tag
                        v-for="(state, stateIndex) in node.states"
                        :key="stateIndex"
                        color="cyan"
                        style="margin: 2px"
                      >
                        {{ typeof state === 'object' ? state.desc : state }}
                        <span v-if="typeof state === 'object' && state.metaCatalogEnum">
                          （{{ state.metaCatalogEnum }}）
                        </span>
                      </h-tag>
                    </div>
                  </div>
                  <div v-if="node.configs && node.configs.length" class="node-configs">
                    <strong>配置项：</strong>
                    <ul>
                      <li v-for="(config, configIndex) in node.configs" :key="configIndex" class="config-item">
                        <span class="config-text">
                          <span v-if="config.type" class="config-type">{{ config.type }}</span>
                          <span v-if="config.metaKey" class="config-meta-key">({{ config.metaKey }})</span>
                          <span class="config-separator">：</span>
                          <span class="config-value">{{ config.configJson }}</span>
                        </span>
                        <span
                          v-if="config.reverseDisplayParam"
                          class="config-display"
                          :title="config.reverseDisplayParam"
                        >
                          ({{ getDisplayText(config.reverseDisplayParam) }})
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
              </h-card>
            </h-timeline-item>
          </h-timeline>
          <h-empty v-else description="暂无节点数据" />
        </div>
      </div>

      <div class="empty-data" v-else-if="!loading">
        <h-empty description="暂无数据" />
      </div>
    </h-spin>
  </div>
</template>

<style scoped lang="less">
.process-detail-container {
  background-color: #fff;
  padding: 20px;
  height: 100%;
  width: 100%;
  overflow: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.nodes-container {
  padding: 20px 0;
}

.node-card {
  margin-bottom: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.node-title {
  display: flex;
  align-items: center;

  .node-seq {
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #1890ff;
    color: #fff;
    border-radius: 50%;
    margin-right: 8px;
  }

  .node-name {
    font-weight: 500;
    margin-right: 8px;
  }
}

.node-content {
  padding: 8px 0;

  p {
    margin-bottom: 8px;
  }
}

.node-states {
  margin-top: 4px;
}

.node-configs {
  margin-top: 8px;

  ul {
    margin-top: 4px;
    padding-left: 20px;
  }

  .config-item {
    margin-bottom: 4px;

    .config-name {
      font-weight: 500;
      color: #1890ff;
      margin-right: 8px;
    }

    .config-value {
      color: #333;
      // margin-right: 8px;
      font-family: monospace;
    }

    .config-display {
      color: #666;
      font-size: 12px;
      font-style: italic;
      // margin-left: 8px;
    }

    .config-text {
      display: inline;
      line-height: 1.5;
    }

    .config-type {
      color: #333;
      font-weight: 500;
    }

    .config-meta-key {
      color: #666;
      font-size: 13px;
    }

    .config-separator {
      color: #333;
      margin: 0 2px;
    }

    .config-value {
      color: #1890ff;
      font-weight: 500;
    }
  }
}

.empty-data {
  text-align: center;
  padding: 40px;
}
</style>

<script lang="ts" setup>
import {
  Button as hButton,
  Card as hCard,
  Row as hRow,
  Col as hCol,
} from 'ant-design-vue';
import { onMounted, ref } from "vue";
import type { Ref } from "vue";
import {
  IAnnualBudget,
  hotelLevelAllConstant
} from '@haierbusiness-front/common-libs';
import { formatNumberThousands } from '@haierbusiness-front/utils';
import { annualBudgetApi } from '@haierbusiness-front/apis';
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();
const loading = ref(false);

const from = ref();

const annualBudget: Ref<IAnnualBudget> = ref({});

// 获取详情数据
const getDetail = async () => {
  const id = Number(route.query.id);
  if (!id) return;

  loading.value = true;
  try {
    const res = await annualBudgetApi.details(id);
    annualBudget.value = res;
    if (annualBudget.value.item) {
      annualBudget.value.item = getSelectedLabels(annualBudget.value.item, options)
    }
    if (annualBudget.value.hotelLevel) {
      annualBudget.value.hotelLevel = hotelLevelAllConstant.ofType(annualBudget.value.hotelLevel)?.desc
    }
    console.log(annualBudget.value, "annualBudget.value");

  } catch (error) {
    console.error('获取详情失败:', error);
  } finally {
    loading.value = false;
  }
};


const getSelectedLabels = (value: number | string, options: any[]) => {
  return options
    .filter(opt => (value & Number(opt.value)) === Number(opt.value))
    .map(opt => opt.label)
    .join(',')
}

const options = [
  { value: '1', label: '住宿' },
  { value: '2', label: '餐饮' },
  { value: '4', label: '会场' },
  { value: '8', label: '用车' },
]



onMounted(() => {
  getDetail()
})
</script>

<template style="background-color:#fff">
  <div class="container">
    <h-card :loading="loading">
      <div class="info-section">
        <h3><span></span>会议信息</h3>
        <h-row :gutter="[16, 16]">
          <h-col :span="6">
            <div class="info-item">
              <span class="label">会议月份：</span>
              <span class="value">{{ annualBudget.miceTime }}</span>
            </div>
          </h-col>
          <h-col :span="5">
            <div class="info-item">
              <span class="label">会议天数：</span>
              <span class="value">{{ annualBudget.day }}天</span>
            </div>
          </h-col>
          <h-col :span="8">
            <div class="info-item hidden">
              <span class="label">会议名称：</span>
              <span class="value">{{ annualBudget.name }}</span>
            </div>
          </h-col>
          <h-col :span="5">
            <div class="info-item">
              <span class="label">预算金额：</span>
              <span class="value">{{ formatNumberThousands(annualBudget.budget) }} 元</span>
            </div>
          </h-col>
        </h-row>
        <h-row :gutter="[16, 16]" style="margin-top: 15px;">
          <h-col :span="6">
            <div class="info-item">
              <span class="label">会议地点：</span>
              <span class="value">{{ annualBudget.place }}</span>
            </div>
          </h-col>
          <h-col :span="5">
            <div class="info-item">
              <span class="label">参加人数：</span>
              <span class="value">{{ annualBudget.personTotal }}人</span>
            </div>
          </h-col>
          <h-col :span="8">
            <div class="info-item">
              <span class="label">酒店星级：</span>
              <span class="value">{{ annualBudget.hotelLevel }}</span>
            </div>
          </h-col>
          <h-col :span="5">
            <div class="info-item">
              <span class="label">是否布展：</span>
              <span class="value">{{ annualBudget.isCloth ? '是' : '否' }}</span>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <span class="label">需求项目：</span>
              <span class="value">{{ annualBudget.item }}</span>
            </div>
          </h-col>
        </h-row>
      </div>
      <div class="info-section">
        <h3><span></span>会议描述</h3>
        <h-row :gutter="[16, 16]">
          <h-col :span="16">
            <div class="info-item">
              <span class="label">会议描述：</span>
              <span class="value">{{ annualBudget.description }}</span>
            </div>
          </h-col>
        </h-row>
      </div>
    </h-card>
  </div>

</template>


<style lang="less" scoped>
.container{
  height: 100%;
  padding: 15px;
  background-color: #fff;
}
.important {
  color: red;
}

.annualBudget {
  width: 100%;
  padding: 20px 0;
  min-height: 100vh;
  background-color: #fff;
}

.form {
  width: 50%;
  margin: 20px 100px;
}

.cancel-button {
  margin-right: 40px;
}

.info-section {
  margin-bottom: 24px;

  h3 {
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 500;
  }
}

.info-item {
  display: flex;
  align-items: flex-start;

  .label {
    color: rgba(0, 0, 0, 0.55);
    // min-width: 130px;
  }

  .value {
    color: #000;
    flex: 1;
    word-break: break-all;
  }
}

h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;

  span {
    display: inline-block;
    width: 4px;
    height: 20px;
    margin-right: 3px;
    background: #1868DB;
  }
}

.ant-card-bordered {
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0px 3px 12px 0px rgba(1, 12, 51, 0.07);
}

.hidden {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.hidden:hover::after {
  content: attr(data-content);
  position: absolute;
  background: white;
  border: 1px solid #ddd;
  padding: 5px;
}
</style>
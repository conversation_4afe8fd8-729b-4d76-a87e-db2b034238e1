<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  Image as hImage,
  ImagePreviewGroup as hImagePreviewGroup,
  Input as hInput,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { pascalCaseApi } from '@haierbusiness-front/apis';
import { IPascalCaseFilter, IPascalCase } from '@haierbusiness-front/common-libs';
import { computed, ref, onMounted, h } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog } from '@haierbusiness-front/composables';
import router from '../../../router';
// const router = useRouter()

const currentRouter = ref();

onMounted(async () => {
  currentRouter.value = await router;
  // 初始化加载数据
  listApiRun({
    pageNum: 1,
    pageSize: 10,
  });
});

const columns: ColumnType[] = [
  {
    title: '礼品名称',
    dataIndex: 'presentName',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: 'SPU',
    dataIndex: 'spuId',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '图片',
    dataIndex: 'path',
    width: '120px',
    align: 'center',
    // customRender: ({ text }) => {
    //   return h(
    //     hImagePreviewGroup,
    //     {},
    //     {
    //       default: () =>
    //         text.map((img: { path: string }, index: number) =>
    //           h(hImage, {
    //             width: 80,
    //             src: img.path,
    //             style: {
    //               display: index === 0 ? 'inline-block' : 'none',
    //             },
    //           }),
    //         ),
    //     },
    //   );
    // },
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '120px',
    align: 'center',
    customRender: ({ record }: { record: IPascalCase }) => {
      return h(
        hTag,
        {
          color: record.state === true ? 'green' : 'red',
        },
        () => (record.state === true ? '上架' : '下架'),
      );
    },
  },
  {
    title: '市场价',
    dataIndex: 'marketPrice',
    width: '120px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text) {
        return ''
      } else {
        return text + '元'
      }
    },
  },
  {
    title: '成本价',
    dataIndex: 'costPrice',
    width: '120px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text) {
        return 0 + '元'
      } else {
        return text + '元'
      }
    },
  },
  {
    title: '销售价',
    dataIndex: 'salePrice',
    width: '120px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text) {
        return 0 + '元'
      } else {
        return text + '元'
      }
    },
  },
  {
    title: '利润率',
    dataIndex: 'profit',
    width: '120px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text) {
        return 0+'%'
      } else {
        return text + '%'
      }
    },
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center',
  },
];
const searchParam = ref<IPascalCaseFilter & { state?: string }>({});
const { data, run: listApiRun, loading, current, pageSize } = usePagination(pascalCaseApi.list);

const reset = () => {
  searchParam.value = {};
};

const searchData = () => {
  const params = { ...searchParam.value };
  // 将字符串类型的state转换为布尔值
  if (params.state !== undefined && params.state !== '') {
    (params as any).state = params.state === 'true';
  } else {
    delete (params as any).state;
  }
  listApiRun({
    ...params,
    pageNum: 1,
    pageSize: 10,
  });
};

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  const params = { ...searchParam.value };
  // 将字符串类型的state转换为布尔值
  if (params.state !== undefined && params.state !== '') {
    (params as any).state = params.state === 'true';
  } else {
    delete (params as any).state;
  }
  listApiRun({
    ...params,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};
const edit = (id?: number) => {
  currentRouter.value.push({ path: '/mice-merchant/pascalCase/component/giftEdit', query: { id: id } });
};

// 新增编辑功能
const editItem = (id: number) => {
  currentRouter.value.push({ 
    path: '/mice-merchant/pascalCase/component/giftEdit', 
    query: { id: id, mode: 'edit' } 
  });
};

const add = () => {
  currentRouter.value.push({ path: '/mice-merchant/pascalCase/component/giftEdit' });
};

// 上架/下架切换
const toggleStatus = async (record: IPascalCase) => {
  try {
    // 调用 API 切换状态，传递id和新的state值
    await pascalCaseApi.updateStatus({
      id: record.id,
      state: !record.state, // 上架传true，下架传false
    });
    // 刷新数据
    listApiRun({
      ...searchParam.value,
      pageNum: data.value?.pageNum,
      pageSize: data.value?.pageSize,
    });
  } catch (error) {
    console.error('状态切换失败:', error);
  }
};
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="presentName">礼品名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.presentName" placeholder="请输入礼品名称" style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="state">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.state" placeholder="请选择状态" style="width: 100%" allow-clear>
              <h-select-option value="true">上架</h-select-option>
              <h-select-option value="false">下架</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="searchData">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="12" style="text-align: left">
            <h-button type="primary" @click="add()">
              <PlusOutlined /> 新增
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="(record) => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :loading="loading" @change="handleTableChange($event as any)" :scroll="{ x:1400 }">
          <template #bodyCell="{ column, record }" >
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="edit(record.id)">查看</h-button>
              <h-button type="link" @click="editItem(record.id)">编辑</h-button>
              <h-button type="link" :style="{ color: record.state ? '#ff4d4f' : '#52c41a' }"
                @click="toggleStatus(record)">
                {{ record.state ? '下架' : '上架' }}
              </h-button>
            </template>
            <template v-if="column.dataIndex === 'path'">
              <!-- 方案1：使用 a-image 但禁用预览功能 -->
              <!-- <a-image v-if="record.path && record.path.length > 0" :src="record.path[0].path" class="preview-image" /> -->
              <a-image-preview-group v-if="record.path && record.path.length > 0">
                <a-image class="preview-image" :src="item.path" v-for="(item, index) in record.path"
                  :style="{ display: index === 0 ? 'block' : 'none' }" />
              </a-image-preview-group>
              <span v-else class="no-image-text">暂无图片</span>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

/* 隐藏所有图片预览相关的遮罩和图标 */
:deep(.ant-image-mask) {
  display: none !important;
}

:deep(.ant-image-mask-info) {
  display: none !important;
}

:deep(.ant-image-preview-mask) {
  display: none !important;
}

:deep(.ant-image .ant-image-mask) {
  display: none !important;
}

:deep(.ant-image:hover .ant-image-mask) {
  display: none !important;
}

:deep(.preview-image) {
  width: 60px;
  height: 60px !important;
  object-fit: cover;
  border-radius: 4px;
  cursor: default !important;
  /* 移除指针样式 */
}

.no-image-text {
  color: #999;
  font-size: 12px;
  text-align: center;
  display: inline-block;
  width: 60px;
  height: 60px;
  line-height: 60px;
}
</style>

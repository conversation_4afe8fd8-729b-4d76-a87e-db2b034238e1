<template>
  <div class="container">
    <a-modal v-model:open="item.visible" title="公告通知" v-for="(item, index) in noticeList.list" :key="item.id"
      :ref="handleImmediate(item, index)" @cancel="handlecancel(index)" width="50%">
      <template #footer>
        <a-button type="primary" @click="handleOk(item,index)">我已知晓</a-button>
      </template>
      <div class="notice-container">
        <div class="notice-content">
          <h2 style="text-align: center;">{{ notice?.title }}</h2>
          <div class="notice-date">
            <p>发布时间：{{ notice?.gmtCreate }}</p>
            <p>发布人：{{ notice?.createName }}</p>
          </div>
          <div :ref="el => noticeContent = el" class="noticeContent"></div>
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import { announcementNoticeApi } from '@haierbusiness-front/apis';
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';
import { usePagination } from 'vue-request';
import { IMiceBidNotice } from '@haierbusiness-front/common-libs'

const emit = defineEmits(["cancel", "ok"]);

// 确认处理
const handleOk = async (item: any,index: number) => {
  if (index < 0 || index >= data.value.length) return
  noticeList.currentIndex = index + 1
  console.log(item);
  try {
    const parmas = {
      id:item.id,
      effectScope:1,
      effectScopes:item.effectScope
    }
    await announcementNoticeApi.read(parmas)
    .then(()=>{
      // message.success('已读成功')
    })

  } catch (error) {
    console.error('获取详情失败:', error);
  } finally {
    confirmLoading.value = false;
  }
}
const handlecancel = (index: number) => {
  noticeList.currentIndex = index + 1
};

const handleImmediate = (item: any, index: number) => {
  // console.log('立即执行默认操作')
  item.visible
  if (item.visible) {
    noticeDetails(item.id)
  }

}

const notice = ref()
const noticeContent = ref()

//通知详情
const noticeDetails = async (id: number) => {
  if (!id) return;

  confirmLoading.value = true;
  try {
    const res = await announcementNoticeApi.details(id);
    notice.value = res;
    console.log(notice.value);

  } catch (error) {
    console.error('获取详情失败:', error);
  } finally {
    confirmLoading.value = false;
  }
}


onMounted(async () => {
  // 初始加载数据
  listApiRun({
    effectScope:1,
  });
})
const confirmLoading = ref(false);
const {
  data,
  run: listApiRun,
  loading,
} = usePagination(announcementNoticeApi.noticeList);

// 列表
const noticeList = reactive({
  currentIndex: 0,
  get list() {
    return data.value?.map((item: any, i: number) => ({
      ...item,
      visible: i === this.currentIndex
    })) || [] as IMiceBidNotice[]
  }
})
//切换逻辑
watch(() => data.value, (newVal) => {
  if (!newVal?.length) return
  if (noticeList.currentIndex >= newVal.length) {
    noticeList.currentIndex = 0
  }
}, { immediate: true })

watch(() => notice.value, (newVal) => {
  if (newVal?.informContent) {
    nextTick(() => {
      noticeContent.value.innerHTML = newVal.informContent;
    });
  }
}, { immediate: true });
</script>

<style lang="less" scoped>
.notice-date {
  display: flex;
  justify-content: center;
  margin: 5px 0;

  p {
    margin-bottom: 0;
    margin-right: 25px;
  }
}

.notice-container {
  max-height: 400px;
  overflow: hidden;
  overflow-y: auto;
}


::v-deep(.noticeContent) {
  overflow: visible;
  img {
    width: 100px;
    height: 100px;
  }
  ul > li {
    list-style-type: circle;
    list-style-position: inside;
  }

  ol > li {
    list-style-type: decimal;
    list-style-position: inside;
  }
}
</style>
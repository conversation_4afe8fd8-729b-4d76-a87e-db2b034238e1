<template>
  <div style="
      background-color: #ffff;
      height: 100%;
      width: 100%;
      padding: 10px 10px 0px 10px;
      overflow: auto;
    ">

    <h-row :align="'middle'">

      <h-col :span="24" style="margin-bottom: 10px">

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_create_user">发布人工号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="search_create_user" v-model:value="searchParams.create_user.value" placeholder=""
                     autocomplete="off"
                     allow-clear/>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_create_user_name">发布人名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="search_create_user_name" v-model:value="searchParams.create_user_name.value" placeholder=""
                     autocomplete="off"
                     allow-clear/>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_piggyback_status">是否删除：</label>
          </h-col>
          <h-col :span="4">
            <h-select
                placeholder=""
                v-model:value="searchParams.is_delete.value"
                :options="preData.isDeleteStatuses"
                allow-clear
                style="width: 100%"
                :field-names="{ label: 'name', value: 'code' }"
            ></h-select>
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_piggyback_status">需求状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
                placeholder=""
                v-model:value="searchParams.piggyback_status.value"
                :options="preData.piggybackStatuses"
                allow-clear
                style="width: 100%"
                :field-names="{ label: 'name', value: 'code' }"
            ></h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_create_time">需求发布日期：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchParams.create_time.value" value-format="YYYY-MM-DD"
                            style="width: 100%"/>
          </h-col>

        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="handleReset">重置</h-button>
            <h-button style="margin-right: 10px" type="primary" @click="doFilter">
              <SearchOutlined/>
              查询
            </h-button>
            <h-button
                type="primary"
                style="margin-right: 10px"
                v-if="!pagination.disabled"
                :loading="downloading"
                @click="download"
            >
              <UploadOutlined/>
              导出
            </h-button>
          </h-col>
        </h-row>

      </h-col>

      <h-col :span="24">
        <h-table
            :columns="columns"
            :row-key="(record) => record.order_code"
            :size="'small'"
            :data-source="data"
            :pagination="pagination"
            :scroll="{ y: 550, x: 1500 }"
            :loading="loading"
            @change="onPageChange"
        >
          <template #emptyText v-if="pagination.disabled">
            <div>暂无权限，<a @click="goApplyDetail">去申请</a></div>
          </template>

          <template #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">

            <!-- 日期选择器 -->
            <div v-if="column.title.indexOf('时间')!=-1||column.title.indexOf('日期')!=-1" style="padding: 8px">

              <h-range-picker
                  v-model:value="searchParams[column.key].value"
                  value-format="YYYY-MM-DD"
                  style="width: 218px; margin-bottom: 8px;"
              />

              <div style="display: block">
                <a-button
                    type="primary"
                    size="small"
                    style="width: 90px; margin-right: 8px"
                    @click="doFilter"
                >
                  <template #icon>
                    <SearchOutlined/>
                  </template>
                  搜索
                </a-button>
                <a-button size="small" style="width: 90px" @click="handleReset">
                  重置
                </a-button>
              </div>
            </div>

            <!-- 文本搜索框 -->
            <div v-else style="padding: 8px">
              <h-input
                  ref="searchInput"
                  :placeholder="`搜索${column.title}`"
                  v-model:value="searchParams[column.key].value"
                  style="width: 188px; margin-bottom: 8px;"
                  allow-clear
                  @pressEnter="doFilter"
              />
              <div style="display: block">
                <a-button
                    type="primary"
                    size="small"
                    style="width: 90px; margin-right: 8px"
                    @click="doFilter"
                >
                  <template #icon>
                    <SearchOutlined/>
                  </template>
                  搜索
                </a-button>
                <a-button size="small" style="width: 90px" @click="handleReset">
                  重置
                </a-button>
              </div>
            </div>
          </template>

          <template #customFilterIcon="{ filtered }">
            <SearchOutlined :style="{ color: filtered ? '#108ee9' : undefined }"/>
          </template>

        </h-table>

      </h-col>
    </h-row>
  </div>
</template>

<script setup lang="ts">

import {
  Button as hButton,
  Col as hCol,
  Input as hInput,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  Table as hTable,
} from 'ant-design-vue';
import {onMounted, reactive} from "vue";
import {ReportFilter, ReportType,} from "@haierbusiness-front/common-libs";

import {aggregatorsToColumn, helperColumns} from "../columns";
import {SearchOutlined, UploadOutlined} from "@ant-design/icons-vue";
import {reset} from "@haierbusiness-front/utils";
import {useSearch} from "@/composables/useSearch";
import {reportApi} from "@haierbusiness-front/apis";

const preData = {
  piggybackStatuses: [{
    code: '10',
    name: '待匹配'
  }, {
    code: '20',
    name: '匹配成功'
  }, {
    code: '30',
    name: '已取消'
  }, {
    code: '40',
    name: '已过期'
  }],
  isDeleteStatuses: [{
    code: '0',
    name: '正常'
  }, {
    code: '1',
    name: '已删除'
  }],
}

// 点击查询
const doFilter = () => {

  // 1、查询条件如果定义了过滤方式，按定义过滤方式执行
  // 2、如果查询条件没有定义过滤方式，按默认过滤方式执行

  let defaultParams = {}
  let filters: any[] = []
  let keys = Object.keys(searchParams);

  keys.forEach((key) => {
    if (searchParams[key] && searchParams[key].value && searchParams[key].filter) {
      if (Array.isArray(searchParams[key].value)) {
        if (searchParams[key].value.length == 2) {
          filters.push({
            aggOperator: null,
            column: [key],
            sqlOperator: "LGT",
            values: [
              {
                value: searchParams[key].value[0],
                valueType: searchParams[key].valueType,
              },
            ],
          })
        }

      } else {
        filters.push({
          aggOperator: null,
          column: [key],
          sqlOperator: searchParams[key].filter,
          values: [
            {
              value: searchParams[key].value,
              valueType: searchParams[key].valueType,
            },
          ],
        })
      }
    } else if (searchParams[key] && searchParams[key].value) {
      if (!Array.isArray(searchParams[key].value))
        defaultParams[key] = searchParams[key].value
      else if (searchParams[key].value.length > 0)
        defaultParams[key] = searchParams[key].value
    }
  })

  searchKey.datartParams.defaultFilters = filters
  removeKey(finalSearchParams, ["datartParams", "fileName"]);
  Object.assign(finalSearchParams, searchKey, defaultParams)

  onFilterChange()
}

// 头部查询条件
const searchParams = reactive({
  create_user: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  create_user_name: {
    value: null,
    valueType: "STRING",
    filter: 'LIKE'
  },
  create_time: {
    value: [] as string[],
  },
  from_city_name: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  dest_city_name: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  object_type_name: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  is_delete: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  is_delete_name: {
    value: null,
    valueType: "STRING",
    filter: 'LIKE'
  },
  piggyback_status: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  piggyback_status_name: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  accept_user_code: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  accept_user_name: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  accept_update_time: {
    value: [] as string[],
  }
})

// 默认查询定义
const searchKey = reactive<ReportFilter>({
  datartParams: {
    moduleType: 1,
    type: "helper",
    viewId: "03afe6a246a3491cb48cfffe36fe05c8",
    aggregators: helperColumns,
    defaultFilters: [],
    orders: [{
      "column": [
        "create_time"
      ],
      "operator": "DESC"
    }],
  },
  fileName: "创客帮",
});

// 最终查询参数
const finalSearchParams = Object.assign({}, searchKey)

const {
  data,
  onFilterChange,
  pagination,
  loading,
  onPageChange,
  downloading,
  download
} = useSearch<ReportType, ReportFilter>(reportApi, finalSearchParams, "banquet");

const handleReset = () => {
  reset(searchKey, ["datartParams", 'fileName']);
  reset(finalSearchParams);
  resetValue(searchParams);
  doFilter();
};

const resetValue = (params: any) => {
  Object.keys(params).forEach((key) => {
    params[key].value = null;
  });
  return params;
}
const removeKey = (params: any, ignore?: Array<string>) => {
  Object.keys(params).forEach((key) => {
    if (!ignore) delete params[key];
    if (ignore && ignore.filter((item) => item == key).length == 0) {
      delete params[key];
    }
  });
  return params;
}

const columns = aggregatorsToColumn(helperColumns);

onMounted(() => {
  // getAreaList({ type: "hotel", moduleType: 1, keyword: "" })
});
</script>

<style scoped lang="less">

</style>
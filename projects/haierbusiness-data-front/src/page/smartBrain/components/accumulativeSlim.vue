<template>
  <h-row
    type="flex"
    :gutter="[30, 10]"
    style="margin-left: 0; margin-right: 0; height: 100%;row-gap:0;overflow:auto;align-content: flex-start;"
    background="rgba(0,0,0,0)"
    v-loading="loading"
  >
    <h-col :span="8" class="content" v-for="(column, index) in columns" :key="index">
      <div>
        <div class="num">
          <CountTo :start-val="0" :decimals="column.name[0] == '平均折扣'||column.name[0].indexOf('政策节省')!=-1 ? 2 : 0" :end-val="rows[index]" />
          <span class="unit" v-if="column.name[0] == '人次' && rows[rows.length - 1] == 'unit'">w</span>
          <span class="unit" v-if="column.name[0] == '成交金额' || column.name[0].indexOf('政策节省') !=-1|| column.name[0] == '毛利'|| column.name[0] == '成本'|| column.name[0] == '中标金额'|| column.name[0] == '降费金额'|| column.name[0] == '销售金额'">w</span>
          <span
            class="unit"
            v-if="column.name[0] == '退票率' || column.name[0] == '改期率' || column.name[0] == '投保率'|| column.name[0] == '毛利率'|| column.name[0] == '降费率'"
            >%</span
          >
        </div>
        <div class="title"><span>▶</span>{{ column.name[0] }}</div>
      </div>
    </h-col>
    <h-col :span="8" class="content" v-for="(item,index) in (columns.length % 3!=0?3 - (columns.length % 3):0)" :key="index">
      <div>
      </div>
    </h-col>
  </h-row>
</template>
<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  Button as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from 'ant-design-vue';
import CountTo from '@/components/vue-count-to/src';
import { queryTrainAccumulative as queryAccumulative } from '@haierbusiness-front/apis/src/data/board/travel';
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { EventBus } from '../../../page/board/eventBus';
import { smartBrainApi } from '@haierbusiness-front/apis';
const columns: any = ref([]);
const rows = ref([]);
const loading = ref(false);
const unsubscribe = EventBus.on((event, params) => {
  if (event == 'refresh') {
    queryData(params);
  }
});
const props = defineProps({
  height: {
    type: Number,
    default: 33,
  },
  id: {
    type: [String, Number],
    default:null,
  },
  echartsJson: {
    type: String,
    default: '',
  },
  // 图标类型
  dataType: {
    type: String,
    default: '',
  },
  searchForm: {
    type: Object,
    default: {},
  },
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
  loading.value = true;
  const data = await smartBrainApi.queryCommonData({ brainReportIndicatorId: props.id, ...props.searchForm });
  columns.value = data.columns ?? [];
  if(props.id==58){
    data.columns.splice(3,2)
    data.rows[0].splice(3,2)
  }
  data.columns.forEach((item, index) => {
    if (item.name[0] == '人次') {
      // data.rows[0][index] =
      //   data.rows[0][index] > 100000 ? ((data.rows[0][index] / 1000).toFixed(0) as any) - 0 : data.rows[0][index];
      // if (data.rows[0][index] > 100000) {
      //   data.rows[0].push('unit');
      // }
    }
    if (item.name[0] == '成交金额' || item.name[0].indexOf('政策节省') !=-1|| item.name[0] == '毛利'|| item.name[0] == '成本'|| item.name[0] == '中标金额'||item.name[0] =='降费金额'||item.name[0] =='销售金额') {
      data.rows[0][index] = ((data.rows[0][index] / 10000).toFixed(2) as any) - 0;
    }
    if (item.name[0] == '退票率' || item.name[0] == '改期率' || item.name[0] == '投保率'||item.name[0] == '毛利率'||item.name[0] == '降费率') {
      data.rows[0][index] = ((data.rows[0][index] * 100).toFixed(0) as any) - 0;
    }
  });
  rows.value = data.rows[0];
  loading.value = false;
  let arrColums = [];
  if (columns.value.length > 0) {
    columns.value.forEach((item, i) => {
      if (item.name[0] != '政策节省') {
        arrColums.push(item);
      }
      if (item.name[0] == '政策节省') {
        // rows.value = rows.value.filter((each, index) => index != i);
        arrColums.push(item);
      }
    });
  }
  columns.value = arrColums;
  console.log(columns.value, '9999');
};

onMounted(() => {
  queryData();
});
// 在组件销毁前移除事件监听器
onBeforeUnmount(() => {
  unsubscribe();
});
</script>
<style scoped lang="less">
.content {
  height: 11vh;
  display: flex;
  align-items: center;
  justify-content: center;
  // background-image: url(@/assets/image/bigscreen/bg-accumulative-content.png);
  background-size: 90% 90%;
  background-repeat: no-repeat;
  background-position: center;
  // margin-top:24px;
  border-top: 2px dashed #0073e5;
  border-right: 2px dashed #0073e5;
  padding: 16px;
  &:nth-child(3n + 3) {
    border-right: none;
  }
  &:nth-child(-n + 3) {
    border-top: none;
  }
}
.num {
  font-size: 22px;
  color: #0073e5;
  font-weight: 500;
  // width: 70px;
  text-align: center;
  margin-bottom: 5px;
  .unit {
    font-size: 14px;
  }
}
.title {
  // width: 70px;
  font-size: 10px;
  text-align: center;
  color: #0073e5;
  span {
    margin-right: 5px;
  }
}

@media screen and (min-width: 1700px) {
  .num {
    font-size: 32px;
    text-align: center;
  }
  .title {
    font-size: 12px;
    text-align: center;
  }
}

:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.5);
}
</style>

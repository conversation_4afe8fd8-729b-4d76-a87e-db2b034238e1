<script lang="ts" setup>
import {
  Button,
  message,
  Modal,
} from 'ant-design-vue';
import { computed, h, onMounted, reactive, ref, watch } from 'vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import type { SizeType } from 'ant-design-vue/es/config-provider';
import { useRoute, useRouter } from 'vue-router';
import dayjs, { Dayjs } from 'dayjs';
import { meetingSignInApi } from '@haierbusiness-front/apis';
import { usePagination } from 'vue-request';
import {
  IMeetingSignInDetails,
  IMeetingSignInRules,
  SignInTypeMap,
  SignInApproveStatusMap,
} from '@haierbusiness-front/common-libs';
// @ts-ignore
import signInMap from '../components/signInMap/index.vue';
//解决警告
const props = defineProps({
  query: {
    type: Object,
    default: () => ({})
  }
})
const route = useRoute();
const router = useRouter();
// 从URL获取会议信息
const miceInfoId = route.query.miceInfoId;
const miceInfoName = route.query.miceInfoName;

// 补签到弹框
const supplementSignInModalVisible = ref(false);
const supplementSignInForm = reactive({
  nickName: '',
  username: '',
  phoneNo: '',
  supplementDate: null as Dayjs | null,
  reasonType: '其他',
  reasonText: '',
  record: null as any,
});

const activeKey = ref<number>(2);
const activeTitle = reactive([
  { key: 1, title: '签到规则' },
  { key: 2, title: '签到明细' },
]);
// 统计信息tab状态
const statActiveKey = ref<string>('total');
//页面标题
const currentTitle = computed(() => {
  return activeTitle.find((item) => item.key === activeKey.value)?.title || '';
});
//签到规则
const signInRules = ref<IMeetingSignInRules>();
//签到明细
const signInDetails = ref<IMeetingSignInDetails>();
//全体统计数据
const totalStatistics = ref({
  total: 0,
  checkedIn: 0,
  notCheckedIn: 0,
});
//地图相关
const mapRef = ref();
const currentLocation = ref({
  address: '',
  name: '',
  city: '',
  district: '',
});
//处理位置更新
const handleLocationUpdate = (locationData: any) => {
  currentLocation.value = locationData;
  console.log('签到位置信息:', locationData);
};

//获取签到规则
const signInRulesMethod = async () => {
  const currentMiceInfoId = miceInfoId || id;
  const response = await meetingSignInApi.details(Number(currentMiceInfoId) || 2);
  if (response) {
    signInRules.value = response;
    console.log(signInRules.value, 'signInRules.value');
    console.log('签到位置详情:', signInRules.value?.checkList);

    // 初始化地图
    if (signInRules.value?.checkList?.[0]?.longitude && signInRules.value?.checkList?.[0]?.latitude) {
      setTimeout(() => {
        // 获取签到位置坐标，如果没有则使用默认坐标（上海人民广场）
        let lon = 121.475,
          lat = 31.2304; // 默认上海人民广场坐标

        if (signInRules.value?.checkList?.[0]?.longitude && signInRules.value?.checkList?.[0]?.latitude) {
          lon = parseFloat(signInRules.value.checkList[0].longitude);
          lat = parseFloat(signInRules.value.checkList[0].latitude);
        }

        console.log('父组件准备初始化地图，坐标:', lon, lat);

        if (mapRef.value) {
          mapRef.value.createByMap(lon, lat);
        } else {
          // console.error('mapRef.value 为空');
        }
      }, 3000);
    }
  }
};
//获取签到明细
const { data, run: listApiRun, loading, current, pageSize } = usePagination(meetingSignInApi.list);

const dataSource = computed(() => data.value?.records || []);

// 表格列定义
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 60,
    customRender: ({ index }: { index: number }) => index + 1 + (current.value - 1) * pageSize.value,
  },
  {
    title: '应签到人员',
    dataIndex: 'nickName',
    key: 'nickName',
    width: 120,
  },
  {
    title: '工号',
    dataIndex: 'username',
    key: 'username',
    width: 120,
  },
  {
    title: '手机号',
    dataIndex: 'phoneNo',
    key: 'phoneNo',
    width: 130,
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    key: 'idCard',
    width: 180,
  },
  {
    title: '性别',
    dataIndex: 'sex',
    key: 'sex',
    width: 80,
    customRender: ({ text }: { text: number }) => (text === 1 ? '男' : text === 0 ? '女' : '未知'),
  },
  {
    title: '签到状态',
    dataIndex: 'isCheckIn',
    key: 'isCheckIn',
    width: 100,
    customRender: ({ text }: { text: boolean }) => (text ? '已签到' : '未签到'),
  },
  {
    title: '签到时间',
    dataIndex: 'checkInTime',
    key: 'checkInTime',
    width: 180,
    customRender: ({ text }: { text: string }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
  },
  {
    title: '签到方式',
    dataIndex: 'checkInMethod',
    key: 'checkInMethod',
    width: 100,
    customRender: ({ text }: { text: number }) => {
      return SignInTypeMap.get(text) || '';
    },
  },
  {
    title: '签到人员',
    dataIndex: 'checkInNickName',
    key: 'checkInNickName',
    width: 120,
    customRender: ({ text, record }: { text: string; record: any }) =>
      text ? `${text}(${record.checkInUsername})` : '',
  },
  {
    title: '是否补签到',
    dataIndex: 'isAdditionalCheckIn',
    key: 'isAdditionalCheckIn',
    width: 100,
    customRender: ({ text }: { text: boolean }) => (text ? '补签到' : ''),
  },
  {
    title: '补签到审批状态',
    dataIndex: 'approveState',
    key: 'approveState',
    width: 120,
    customRender: ({ text }: { text: number }) => {
      const statusText = SignInApproveStatusMap.get(text);
      return statusText || '';
    },
  },
  {
    title: '补签到审批人',
    dataIndex: 'approvePerson',
    key: 'approvePerson',
    width: 120,
    customRender: ({ text }: { text: string }) => text || '',
  },
  {
    title: '补签到审批时间',
    dataIndex: 'approveTime',
    key: 'approveTime',
    width: 180,
    customRender: ({ text }: { text: string }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
  },
  {
    title: '操作',
    key: 'action',
    width: 240,
    fixed: 'right' as const,
    customRender: ({ record }: { record: any }) => [
      h(
        Button,
        {
          type: 'link',
          size: 'small',
          onClick: () => handleApplySupplementSignIn(record),
        },
        () => '补签到审批明细',
      ),
      h(
        Button,
        {
          type: 'link',
          size: 'small',
          onClick: () => handleSupplementSignIn(record),
        },
        () => '补签到',
      ),
    ],
  },
];

// 统计数据 - 显示全体统计而不是当前页面统计
const statistics = computed(() => {
  return totalStatistics.value;
});

// 导出签到明细
const exportSignInDetails = async () => {
  // 验证必要参数
  const currentMiceInfoId = miceInfoId || id;
  if (!currentMiceInfoId) {
    message.error('缺少会议ID，无法导出');
    return;
  }

  try {
    message.loading('正在导出签到明细...', 0);

    await meetingSignInApi.export({
      miceInfoId: Number(currentMiceInfoId),
      miceInfoName: (Array.isArray(miceInfoName) ? miceInfoName[0] : miceInfoName) || '会议签到明细',
    });

    message.destroy();
    message.success('导出成功，请查看下载文件');
  } catch (error) {
    message.destroy();
    console.error('导出签到明细失败:', error);
    message.error('导出失败，请重试');
  }
};

// 发送签到通知
const sendSignInNotice = async () => {
  // 验证必要参数
  const currentMiceInfoId = miceInfoId || id;
  if (!currentMiceInfoId) {
    message.error('缺少会议ID，无法发送通知');
    return;
  }

  // 检查是否已获取签到规则
  if (!signInRules.value) {
    message.error('请先获取签到规则信息');
    return;
  }

  // 确认发送
  Modal.confirm({
    title: '确认发送签到通知',
    content: '确定要向所有未签到的参会人员发送签到通知吗？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        message.loading('正在发送签到通知...', 0);

        // 调用发送签到通知接口，传递当前的签到规则
        const result = await meetingSignInApi.sendNotice({});

        message.destroy();

        if (result) {
          message.success('签到通知发送成功');
          // 刷新签到明细数据和统计数据
          loadSignInData();
        }
      } catch (error) {
        message.destroy();
        console.error('发送签到通知失败:', error);
        message.error('发送签到通知失败，请重试');
      }
    },
  });
};

// 处理申请补签到
const handleApplySupplementSignIn = (record: any) => {
  Modal.confirm({
    title: '申请补签到',
    content: `确定要为 ${record.nickName}(${record.username}) 申请补签到吗？`,
    okText: '确认申请',
    cancelText: '取消',
    onOk: async () => {
      try {
        message.loading('正在提交申请...', 0);

        // TODO: 调用补签到申请接口
        // const result = await meetingSignInApi.applySupplementSignIn({
        //   userId: record.userId,
        //   miceInfoId: Number(miceInfoId || id),
        //   reason: '申请补签到'
        // });

        message.destroy();
        message.success('补签到申请已提交，等待审批');

        // 刷新数据
        loadSignInData();
      } catch (error) {
        message.destroy();
        console.error('申请补签到失败:', error);
        message.error('申请补签到失败，请重试');
      }
    },
  });
};

// 处理执行补签到
const handleSupplementSignIn = (record: any) => {
  supplementSignInForm.nickName = record.nickName;
  supplementSignInForm.username = record.username;
  supplementSignInForm.phoneNo = record.phoneNo;
  supplementSignInForm.supplementDate = null;
  supplementSignInForm.reasonType = '其他';
  supplementSignInForm.reasonText = '';
  supplementSignInForm.record = record;
  supplementSignInModalVisible.value = true;
};

const handleSupplementSubmit = async () => {
  try {
    // 简单校验
    if (!supplementSignInForm.supplementDate) {
      message.error('请选择补签日期');
      return;
    }
    if (supplementSignInForm.reasonType === '其他' && !supplementSignInForm.reasonText) {
      message.error('请输入其他原因');
      return;
    }

    const dataToSubmit = {
      checkDetailId: supplementSignInForm.record.id,
      nickname: supplementSignInForm.nickName,
      missingCheckInTime: supplementSignInForm.supplementDate?.format('YYYY-MM-DD HH:mm:ss'),
      missingCheckInReason:
        supplementSignInForm.reasonType === '其他'
          ? supplementSignInForm.reasonText
          : supplementSignInForm.reasonType,
    };

    console.log('补签到提交的值:', dataToSubmit);

    message.loading('正在执行补签到...', 0);

    try {
      await meetingSignInApi.backCheck(dataToSubmit as any);

      message.destroy();
      message.success('补签到执行成功');
      supplementSignInModalVisible.value = false;
      // 刷新数据
      loadSignInData();
    } catch (error) {
      message.destroy();
      console.error('执行补签到失败:', error);
      message.error('执行补签到失败，请重试');
    }
  } catch (error) {
    message.destroy();
    console.error('执行补签到失败:', error);
    message.error('执行补签到失败，请重试');
  }
};

// 处理重新申请补签到
const handleReapplySignIn = (record: any) => {
  Modal.confirm({
    title: '重新申请补签到',
    content: `${record.nickName}(${record.username}) 的补签到申请已被拒绝，确定要重新申请吗？`,
    okText: '重新申请',
    cancelText: '取消',
    onOk: async () => {
      try {
        message.loading('正在重新提交申请...', 0);

        // TODO: 调用重新申请补签到接口
        // const result = await meetingSignInApi.reapplySupplementSignIn({
        //   userId: record.userId,
        //   miceInfoId: Number(miceInfoId || id),
        //   reason: '重新申请补签到'
        // });

        message.destroy();
        message.success('补签到申请已重新提交，等待审批');

        // 刷新数据
        loadSignInData();
      } catch (error) {
        message.destroy();
        console.error('重新申请补签到失败:', error);
        message.error('重新申请补签到失败，请重试');
      }
    },
  });
};

// 监听标签页切换
watch(activeKey, (newValue) => {
  if (newValue === 2) {
    // 切换到签到明细时重新加载数据
    loadSignInData();
  }
});

// 监听统计信息标签页切换
watch(statActiveKey, (newValue) => {
  loadSignInData();
});

// 获取全体统计数据
const loadTotalStatistics = async () => {
  const currentMiceInfoId = miceInfoId || id;
  const baseParams = {
    miceInfoId: Number(currentMiceInfoId) || 1,
    pageNum: 1,
    pageSize: 1, // 只需要获取总数，不需要具体数据
  };

  try {
    // 并行获取已签到、未签到和全部的总数
    const [allResult, checkedResult, uncheckedResult] = await Promise.all([
      meetingSignInApi.list(baseParams),
      meetingSignInApi.list({ ...baseParams, isCheckIn: true } as any),
      meetingSignInApi.list({ ...baseParams, isCheckIn: false } as any),
    ]);

    totalStatistics.value = {
      total: allResult?.total || 0,
      checkedIn: checkedResult?.total || 0,
      notCheckedIn: uncheckedResult?.total || 0,
    };
  } catch (error) {
    console.error('获取统计数据失败:', error);
    // 失败时保持原有数据
  }
};

// 加载签到数据的统一方法
const loadSignInData = () => {
  loadSignInDataWithPagination(1, 10);
  // 同时加载全体统计数据
  loadTotalStatistics();
};

// 带分页参数的加载方法
const loadSignInDataWithPagination = (pageNum: number, pageSize: number) => {
  const currentMiceInfoId = miceInfoId || id;
  const baseParams = {
    miceInfoId: Number(currentMiceInfoId) || 1,
    pageNum,
    pageSize,
  };

  // 根据当前选中的统计标签页添加过滤条件
  if (statActiveKey.value === 'checked') {
    // 已签到
    listApiRun({
      ...baseParams,
      isCheckIn: true,
    } as any);
  } else if (statActiveKey.value === 'unchecked') {
    // 未签到
    listApiRun({
      ...baseParams,
      isCheckIn: false,
    } as any);
  } else {
    // 全部，不传isCheckIn参数
    listApiRun(baseParams);
  }
};

onMounted(() => {
  signInRulesMethod();
  loadSignInData(); // 这里会同时加载签到明细和全体统计数据
});
</script>
<template>
  <div class="container">
    <div class="content">
      <div class="content-top">会议首页/{{ router.currentRoute?.value.meta.title }}/{{ currentTitle }}</div>
      <div class="main">
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane :key="1" tab="签到规则">
            <div class="main-content">
              <a-row :gutter="[16, 16]" class="row-top">
                <a-col :span="4" style="text-align: right">是否需要签到：</a-col>
                <a-col :span="7">{{ signInRules?.isOpen ? '需要签到' : '不需要签到' }}</a-col>
                <a-col :span="4" style="text-align: right">有效签到日期：</a-col>
                <a-col :span="7">{{ signInRules?.checkInStartDate }} ~ {{ signInRules?.checkInEndDate }}</a-col>
              </a-row>
              <a-row :gutter="[16, 16]" class="row-top">
                <a-col :span="4" style="text-align: right">须签到人员：</a-col>
                <a-col :span="7"> 全部参会人 </a-col>
                <a-col :span="4" style="text-align: right">免签人员：</a-col>
                <a-col :span="7">暂无</a-col>
              </a-row>
              <a-row :gutter="[16, 16]" class="row-top">
                <a-col :span="4" style="text-align: right">签到方式：</a-col>
                <a-col :span="7">{{
                  signInRules?.checkInMethod === 1 ? '点签到按钮' : signInRules?.checkInMethod === 2 ? '扫码' : ''
                }}</a-col>
                <a-col :span="4" style="text-align: right">打开小程序自动签到：</a-col>
                <a-col :span="7">{{ signInRules?.isOpenCheckIn ? '是' : '否' }}</a-col>
              </a-row>
              <a-row :gutter="[16, 16]" class="row-top">
                <a-col :span="4" style="text-align: right">已签到二维码：</a-col>
                <!-- <a-col :span="20">{{ signInRules?.checkInCodeUrl }}</a-col> -->
                <a-col :span="20"><a-qrcode error-level="H" :value="signInRules?.checkInCodeUrl || ''"
                    :icon="signInRules?.checkInCodeBgUrl" /></a-col>
              </a-row>
              <a-row :gutter="[16, 16]" class="row-top">
                <a-col :span="4" style="text-align: right">签到负责人：</a-col>
                <a-col :span="7" style="color: blue">{{ signInRules?.checkInHandleNickName }}({{
                  signInRules?.checkInHandleUsername }})</a-col>
                <a-col :span="4" style="text-align: right">签到通知：</a-col>
                <a-col :span="7" style="color: blue">{{ signInRules?.isCheckInNotice ? '是' : '否' }}</a-col>
              </a-row>
              <a-row :gutter="[16, 16]" class="row-top">
                <a-col :span="4" style="text-align: right">签到通知频率：</a-col>
                <a-col :span="7" style="color: blue">{{}}</a-col>
                <a-col :span="4" style="text-align: right">签到二维码背景图：</a-col>
                <a-col :span="7" style="color: blue">{{ signInRules?.checkInCodeBgUrl }}</a-col>
              </a-row>
              <a-row :gutter="[16, 16]" class="row-top">
                <a-col :span="4" style="text-align: right">有效签到范围：</a-col>
                <a-col :span="20">{{ signInRules?.checkInRange }}</a-col>
              </a-row>
              <a-row :gutter="[16, 16]" class="row-top">
                <a-col :span="4" style="text-align: right">签到位置：</a-col>
                <a-col :span="20">{{ signInRules?.checkList?.[0]?.place }}</a-col>
              </a-row>

              <!-- 位置信息显示 -->
              <a-row :gutter="[16, 16]" class="row-top" v-if="currentLocation.address">
                <a-col :span="4" style="text-align: right">详细位置：</a-col>
                <a-col :span="20">
                  <div class="location-info">
                    <span class="location-label">📍</span>
                    <span class="location-name">{{ currentLocation.name || signInRules?.checkList?.[0]?.place }}</span>
                    <span class="location-address">{{ currentLocation.address }}</span>
                  </div>
                </a-col>
              </a-row>

              <!-- 地图显示 -->
              <!-- <a-row :gutter="[16, 16]" class="row-top" v-if="signInRules?.checkList?.[0]?.longitude">
                <a-col :span="4" style="text-align: right">位置地图：</a-col>
                <a-col :span="20">
                  <div class="map-container">
                    <signInMap ref="mapRef" @locationUpdate="handleLocationUpdate"></signInMap>
                  </div>
                </a-col>
              </a-row> -->
            </div>
          </a-tab-pane>
          <a-tab-pane :key="2" tab="签到明细">
            <div class="main-content">
              <!-- 统计信息 -->
              <div class="statistics-container">
                <a-tabs type="card" size="small" v-model:activeKey="statActiveKey">
                  <a-tab-pane key="total">
                    <template #tab>
                      <span class="stat-tab total">全部</span>
                    </template>
                  </a-tab-pane>
                  <a-tab-pane key="checked">
                    <template #tab>
                      <span class="stat-tab checked">已签到 </span>
                    </template>
                  </a-tab-pane>
                  <a-tab-pane key="unchecked">
                    <template #tab>
                      <span class="stat-tab unchecked">未签到 </span>
                    </template>
                  </a-tab-pane>
                </a-tabs>
              </div>

              <!-- 签到明细表格 -->
              <div class="table-container">
                <a-table :columns="columns" :data-source="dataSource" :loading="loading" :pagination="{
                  current: current,
                  pageSize: pageSize,
                  total: data?.total || 0,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  onChange: (page: number, size: number) => {
                    loadSignInDataWithPagination(page, size);
                  },
                  onShowSizeChange: (current: number, size: number) => {
                    loadSignInDataWithPagination(1, size);
                  }
                }" :scroll="{ x: 1800, y: 500 }" size="small" bordered>
                  <template #emptyText>
                    <div style="text-align: center; padding: 20px">
                      <div>暂无签到明细数据</div>
                    </div>
                  </template>
                </a-table>

                <!-- 合计信息 -->
                <div class="table-summary">
                  <span class="summary-text">
                    合计：已签到 <span class="summary-number checked">{{ statistics.checkedIn }}</span> 人， 未签到
                    <span class="summary-number unchecked">{{ statistics.notCheckedIn }}</span> 人
                  </span>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="action-buttons">
                <a-button type="primary" style="margin-right: 12px" @click="exportSignInDetails">
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  导出签到明细
                </a-button>
                <a-button @click="sendSignInNotice">发送签到通知</a-button>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
  <a-modal v-model:open="supplementSignInModalVisible" title="补签到" @ok="handleSupplementSubmit"
    @cancel="supplementSignInModalVisible = false" okText="提交" cancelText="取消">
    <a-form :model="supplementSignInForm" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="姓名：">
        <a-input v-model:value="supplementSignInForm.nickName" disabled />
      </a-form-item>
      <a-form-item label="工号：">
        <a-input v-model:value="supplementSignInForm.username" disabled />
      </a-form-item>
      <a-form-item label="手机号：">
        <a-input v-model:value="supplementSignInForm.phoneNo" disabled />
      </a-form-item>
      <a-form-item label="漏签日期：">
        <a-date-picker v-model:value="supplementSignInForm.supplementDate" style="width: 100%" placeholder="请选择日期" />
      </a-form-item>
      <a-form-item label="补签到原因：" required>
        <a-radio-group v-model:value="supplementSignInForm.reasonType">
          <a-radio value="忘签到">忘签到</a-radio>
          <a-radio value="行程变更">行程变更</a-radio>
          <a-radio value="其他">其他</a-radio>
        </a-radio-group>
        <a-input v-if="supplementSignInForm.reasonType === '其他'" v-model:value="supplementSignInForm.reasonText"
          placeholder="请输入" style="margin-top: 8px" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<style lang="scss" scoped>
* {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
}

ul,
p {
  margin-bottom: 0;
}

ul li {
  list-style: none;
}

.container {
  width: 100%;
  min-height: 100vh;
  padding-bottom: 20px;
  background: #f6f7f9;
}

.content {
  width: 1280px;
  height: auto;
  margin: 0 auto;

  .main {
    width: 100%;
    // height: 800px;
    background-color: #fff;
    padding: 18px 24px;
  }

  .row-top {
    margin-top: 40px;
  }
}

.content-top {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #86909c;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  padding: 16px 0px;
}

.location-info {
  padding: 8px 12px;
  background: #f0f8ff;
  border-left: 4px solid #1890ff;
  border-radius: 4px;
  margin-bottom: 8px;

  .location-label {
    font-size: 16px;
    margin-right: 8px;
  }

  .location-name {
    font-weight: bold;
    color: #333;
    margin-right: 8px;
  }

  .location-address {
    color: #666;
    font-size: 14px;
  }
}

.map-container {
  width: 100%;
  height: 400px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

// 统计标签页样式
.statistics-container {
  margin-bottom: 24px;

  .stat-tabs {
    display: flex;
    gap: 0;
    background: #f5f5f5;
    border-radius: 6px;
    padding: 4px;
    width: fit-content;

    .stat-tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 12px 24px;
      min-width: 100px;
      background: transparent;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;

      .tab-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 4px;
        font-weight: 400;
      }

      .tab-number {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.8);
      }

      &.active {
        background: #1890ff;
        color: white;

        .tab-label {
          color: white;
        }

        .tab-number {
          color: white;
        }
      }
    }
  }
}

// 操作按钮样式
.action-buttons {
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

// 表格容器样式
.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;

  :deep(.ant-table) {
    .ant-table-thead>tr>th {
      background: #fafafa;
      font-weight: 600;
      color: #333;
    }

    .ant-table-tbody>tr:hover>td {
      background: #f5f8ff;
    }

    .ant-table-tbody>tr>td {
      border-bottom: 1px solid #f0f0f0;
    }
  }

  :deep(.ant-pagination) {
    margin: 16px 0;
    text-align: right;
  }
}

// 表格合计信息样式
.table-summary {
  padding: 12px 16px;
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
  text-align: left;

  .summary-text {
    font-size: 14px;
    color: #333;
    font-weight: 500;

    .summary-number {
      font-weight: 600;
      font-size: 16px;

      &.checked {
        color: #52c41a;
      }

      &.unchecked {
        color: #ff4d4f;
      }
    }
  }
}
</style>

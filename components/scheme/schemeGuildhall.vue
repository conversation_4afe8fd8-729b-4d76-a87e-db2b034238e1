<script setup lang="ts">
// 会议厅选择
import { defineEmits, defineProps, onMounted, ref, reactive } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  guildhall: {
    type: String,
    default: '',
  },
  guildhallPhotos: {
    type: Array,
    default: [],
  },
});

const emit = defineEmits(['demandBaseFunc']);

const schemeGuildhallFormRef = ref();
const uploadLoading = ref<boolean>(false);
const isLt50M = ref<boolean>(true);

const formState = reactive<any>({
  guildhallName: '',
  guildhallPhotos: [],
});

// 校验
const rules = {
  guildhallName: [
    { required: true, message: '请填写会议厅名称', trigger: 'change' },
    { min: 1, max: 50, message: '长度不超过50个字符', trigger: 'blur' },
  ],
  guildhallPhotos: [
    {
      required: true,
      message: '请上传附件',
      trigger: 'blur',
    },
  ],
};

// 上传附件 - 删除
const handleRemove: UploadProps['onRemove'] = (file) => {
  formState.activities.forEach((e) => {
    e.paths = [];

    e.fileList.forEach((j) => {
      if (j.name !== file.name) {
        const params = {
          name: j.name,
          url: j.filePath,
        };

        e.paths.push(JSON.stringify(params));
      }
    });
  });
};

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  isLt50M.value = file.size / 1024 / 1024 < 50;

  if (!isLt50M.value) {
    message.error('文件最大不超过50M！');
    return Upload.LIST_IGNORE;
  }

  return isLt50M.value;
};

// 上传附件
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;

      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框

      formState.activities.forEach((e) => {
        e.paths = [];

        e.fileList.forEach((j) => {
          const params = {
            name: j.name,
            url: j.filePath,
          };

          e.paths.push(JSON.stringify(params));
        });
      });
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 提交
const onSubmit = () => {
  schemeGuildhallFormRef.value.validate().then(() => {
    emit('demandBaseFunc', {});
  });
};

onMounted(async () => {
  formState.guildhallName = props.guildhall;
  formState.guildhallPhotos = props.guildhallPhotos.map((e) => {
    const obj = JSON.parse(e);

    return {
      name: obj.name,
      url: obj.url,
    };
  });
});
</script>

<template>
  <!-- 会议厅选择 -->
  <div class="scheme_guildhall">
    <a-form
      class="mt24"
      ref="schemeGuildhallFormRef"
      :model="formState"
      :rules="rules"
      :label-col="{ style: { width: '100px' } }"
    >
      <a-row :gutter="12">
        <a-col :span="24">
          <a-form-item label="会议厅名称：" name="guildhallName">
            <a-input
              v-model:value="formState.guildhallName"
              placeholder="请填写会议厅名称"
              :maxlength="50"
              allow-clear
            />
          </a-form-item>
        </a-col>

        <a-col :span="16">
          <a-form-item label="附件：" name="guildhallPhotos">
            <a-upload
              v-model:fileList="formState.guildhallPhotos"
              :custom-request="uploadRequest"
              accept=".jpg, .png, .jpeg"
              :multiple="false"
              :max-count="99"
              :before-upload="beforeUpload"
              @remove="handleRemove"
            >
              <a-button>
                <upload-outlined></upload-outlined>
                上传会厅图片
              </a-button>
            </a-upload>

            <div :class="['support_extend_tip', 'mt8', isLt50M ? '' : 'err_color']">文件最大不超过50M</div>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<style scoped lang="less">
.scheme_guildhall {
}
</style>

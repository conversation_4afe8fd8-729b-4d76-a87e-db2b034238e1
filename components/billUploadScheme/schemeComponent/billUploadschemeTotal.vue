<script setup lang="ts">
// 方案互动-方案合计
import { message, Modal } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import { AttendantTypeConstant, MaterialTypeConstant } from '@haierbusiness-front/common-libs';

const props = defineProps({
  schemeCacheInfo: {
    type: Object,
    default: {},
  },
  totalPrice: {
    type: Number,
    default: 0,
  },
  leftPlanTotalPrice: {
    type: Number,
    default: 0,
  },
});

//总计金额
const AllPrice = ref()

const emit = defineEmits(['presentPriceEmit']);

const newSchemeList = ref<array>([]);

watch(
  () => props.schemeCacheInfo,
  async () => {
    newSchemeList.value = [];
    let tatalPrice = 0
    if (props.schemeCacheInfo) {
      // 住宿
      if (props.schemeCacheInfo.stays && props.schemeCacheInfo.stays.length > 0) {

        let demandTd2 = '';
        let demandTd3 = 0;

        let roomNum1 = 0;
        let roomNum2 = 0;
        let roomNum3 = 0;

        props.schemeCacheInfo.stays.forEach((e) => {
          if (!e.billUnitPrice) return;

          if (e.roomType === 1) {
            // 大床房（默认1人）
            roomNum1 += e.billRoomNum;
          }
          if (e.roomType === 2) {
            // 双床房
            roomNum2 += e.billRoomNum;
          }
          if (e.roomType === 3) {
            // 套房（默认1人）
            roomNum3 += e.billRoomNum;
          }

          demandTd3 += e.billRoomNum * e.billUnitPrice;
        });

        demandTd2 =
          (roomNum1 ? '大床房*' + roomNum1 + '间夜' : '') +
          ((roomNum1 && roomNum2) || (roomNum1 && roomNum3) ? '，' : '') +
          (roomNum2 ? '双床房*' + roomNum2 + '间夜' : '') +
          (roomNum2 && roomNum3 ? '，' : '') +
          (roomNum3 ? '套房*' + roomNum3 + '间夜' : '');

        newSchemeList.value.push({
          demandTd1: '住宿',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
        tatalPrice += demandTd3
      }

      // 会场
      if (props.schemeCacheInfo.places && props.schemeCacheInfo.places.length > 0) {

        let demandTd2 = '';
        let demandTd3 = 0;

        let isLed = false;
        let isTea = false;

        let ledNum = 0;
        let teaNum = 0;
        let ledPrice = 0;
        let teaPrice = 0;

        props.schemeCacheInfo.places.forEach((e) => {
          if (!e.billUnitPlacePrice) return;

          demandTd3 += e.billUnitPlacePrice || 0;

          if (e.hasLed) {
            if (!e.billUnitLedPrice) return;

            isLed = true;
            ledNum += e.billLedNum || 0;
            ledPrice += e.billUnitLedPrice * e.billLedNum || 0;
            // 单价*LED数量
            demandTd3 += e.billUnitLedPrice * e.billLedNum;
          }
          if (e.hasTea) {
            if (!e.billUnitTeaPrice) return;

            isTea = true;
            teaNum += e.billPersonNum || 0;
            teaPrice += e.billUnitTeaPrice * e.billPersonNum || 0;
            // 茶歇单价*会场人数
            demandTd3 += e.billUnitTeaPrice * e.billPersonNum;
          }
        });

        demandTd2 =
          '会场*' +
          props.schemeCacheInfo.places.length +
          (isLed ? '，LED*' + ledNum + '=' + ledPrice : '') +
          (isTea ? '，茶歇*' + teaNum + '=' + teaPrice : '');

        newSchemeList.value.push({
          demandTd1: '会场',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
        tatalPrice += demandTd3
      }

      // 用餐
      if (props.schemeCacheInfo.caterings && props.schemeCacheInfo.caterings.length > 0) {
        let demandTd2 = '';
        let demandTd3 = 0;

        let cateringsPersonNum = 0;

        props.schemeCacheInfo.caterings.forEach((e) => {
          if (!e.billUnitPrice) return;

          cateringsPersonNum += e.billPersonNum;

          demandTd3 += e.billPersonNum * e.billUnitPrice;
        });

        demandTd2 = cateringsPersonNum ? '用餐人数*' + cateringsPersonNum : '-';

        newSchemeList.value.push({
          demandTd1: '用餐',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
        tatalPrice += demandTd3
      }

      // 用车
      if (props.schemeCacheInfo.vehicles && props.schemeCacheInfo.vehicles.length > 0) {

        let demandTd2 = '';
        let demandTd3 = 0;

        let brandList: any[] = [];
        let brandNumList = [];

        props.schemeCacheInfo.vehicles.forEach((e) => {
          if (!e.billUnitPrice) return;

          if (!brandList.includes(e.brand)) {
            brandList.push(e.brand);
          }

          demandTd3 += e.billUnitPrice * e.billVehicleNum;
        });

        brandList.forEach((e, idx) => {
          const sameCar = props.schemeCacheInfo.vehicles.filter((j) => j.brand === e);
          brandNumList[idx] = 0;

          sameCar.forEach((k) => {
            brandNumList[idx] += k.billVehicleNum;
          });

          demandTd2 += (idx > 0 ? '，' : '') + e + '*' + brandNumList[idx] + '辆';
        });

        newSchemeList.value.push({
          demandTd1: '用车',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
        tatalPrice += demandTd3
      }

      // 服务人员
      if (props.schemeCacheInfo.attendants && props.schemeCacheInfo.attendants.length > 0) {
     
        let demandTd2 = '';
        let demandTd3 = 0;

        let typeList: any[] = [];
        let typeNumList = [];

        props.schemeCacheInfo.attendants.forEach((e) => {
          if (!e.billUnitPrice) return;

          if (!typeList.includes(e.type)) {
            typeList.push(e.type);
          }

          demandTd3 += e.billUnitPrice * e.billPersonNum;
        });

        typeList.forEach((e, idx) => {
          const sameType = props.schemeCacheInfo.attendants.filter((j) => j.type === e);
          typeNumList[idx] = 0;

          sameType.forEach((k) => {
            typeNumList[idx] += k.billPersonNum;
          });

          demandTd2 += (idx > 0 ? '，' : '') + AttendantTypeConstant.ofType(e)?.desc + '*' + typeNumList[idx] + '人';
        });

        newSchemeList.value.push({
          demandTd1: '服务人员',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
        tatalPrice += demandTd3
      }

      // 方案拓展
      if (props.schemeCacheInfo.activities && props.schemeCacheInfo.activities.length > 0) {
        let demandTd2 = '';
        let demandTd3 = 0;

        let activitiesPersonNum = 0;

        props.schemeCacheInfo.activities.forEach((e) => {
          if (!e.billUnitPrice) return;

          activitiesPersonNum += e.billPersonNum;

          demandTd3 += e.billPersonNum * e.billUnitPrice;
        });

        demandTd2 = activitiesPersonNum ? '拓展*' + activitiesPersonNum + '人' : '-';

        newSchemeList.value.push({
          demandTd1: '拓展方案',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
        tatalPrice += demandTd3
      }

      // 保险
      if (props.schemeCacheInfo.insurances && props.schemeCacheInfo.insurances.length > 0) {
        console.log(props.schemeCacheInfo, "props.schemeCacheInfo.insurances");

        let demandTd2 = '';
        let demandTd3 = 0;

        let insList = [];
        let insNumList = [];

        props.schemeCacheInfo.insurances.forEach((e) => {
          if (!insList.includes(e.insuranceName)) {
            insList.push(e.insuranceName);
          }

          demandTd3 += e.billPersonNum * e.billUnitPrice;
        });

        insList.forEach((e, idx) => {
          const sameCar = props.schemeCacheInfo.insurances.filter((j) => j.insuranceName === e);
          insNumList[idx] = 0;

          sameCar.forEach((k) => {
            insNumList[idx] += k.billPersonNum;
          });

          demandTd2 += (idx > 0 ? '，' : '') + e + '*' + insNumList[idx] + '人';
        });

        newSchemeList.value.push({
          demandTd1: '保险',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
        tatalPrice += demandTd3
      }

      // 布展物料
      if (props.schemeCacheInfo.material && Object.keys(props.schemeCacheInfo.material).length > 0) {
        console.log(props.schemeCacheInfo.material, "布展物料账单props.schemeCacheInfo.material");

        let demandTd2 = '';
        let demandTd3 = 0;

        props.schemeCacheInfo.material.materialDetails.forEach((e, idx) => {
          demandTd2 += (idx > 0 ? '，' : '') + MaterialTypeConstant.ofType(e.type)?.desc + '*' + e.billMaterialNum;

          demandTd3 += e.billUnitPrice * e.billMaterialNum;
        });

        newSchemeList.value.push({
          demandTd1: '布展物料',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
        tatalPrice += demandTd3
      }

      // 礼品
      if (props.schemeCacheInfo.presents && props.schemeCacheInfo.presents.length > 0) {
        // console.log(props.schemeCacheInfo, "props.schemeCacheInfo.presents");
        let demandTd2 = '';
        let demandTd3 = 0;

        props.schemeCacheInfo.presents.forEach((e, idx) => {
          demandTd2 +=
            (idx > 0 ? '，' : '') +
            (e.presentDetails[0]?.productName ? e.presentDetails[0]?.productName + '*' : '') +
            (e.presentDetails[0]?.billUnitTeaPrice
              ? e.presentDetails[0]?.billUnitTeaPrice + e.presentDetails[0]?.unit
              : '');

          demandTd3 += e.billTotalPrice;
        });

        newSchemeList.value.push({
          demandTd1: '礼品',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
        tatalPrice += demandTd3
      }

      // 其他
      if (props.schemeCacheInfo.others && props.schemeCacheInfo.others.length > 0) {
        let demandTd2 = '';
        let demandTd3 = 0;

        props.schemeCacheInfo.others.forEach((e, idx) => {
          demandTd2 += (idx > 0 ? '，' : '') + e.itemName + '*' + e.billNum + e.unit;

          demandTd3 += e.billTotalPrice;
        });

        newSchemeList.value.push({
          demandTd1: '其他',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
        tatalPrice += demandTd3
      }

      // 服务费
      if (
        props.schemeCacheInfo.serviceFee &&
        props.schemeCacheInfo.serviceFee.serviceFeeRate !== null &&
        props.schemeCacheInfo.serviceFee.billServiceFeeReal !== null
      ) {

        let demandTd2 = '收取比例' + props.schemeCacheInfo.serviceFee.serviceFeeRate + '%';
        let demandTd3 = props.schemeCacheInfo.serviceFee.billServiceFeeReal;

        newSchemeList.value.push({
          demandTd1: '服务费',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
        tatalPrice += demandTd3
      }

      AllPrice.value = tatalPrice
    }

  },
);

// Plan:方案
// Bill:账单

//各项方案总额和全部总金额校验
const Totalamountverification = () => {
  
  if (props.schemeCacheInfo) {
    // 住宿
    if (props.schemeCacheInfo.stays && props.schemeCacheInfo.stays.length > 0) {


      let staysPlan = 0
      let staysBill = 0;

      props.schemeCacheInfo.stays.forEach((e) => {
        staysPlan += e.schemeRoomNum * e.schemeUnitPrice
        staysBill += e.billRoomNum * e.billUnitPrice;
      });
      
      if (staysBill && staysBill > staysPlan * 1.1) {
        Modal.warning({
          title: '住宿超过对应方案的10%！',
          okText: '确定',
        });
        return false;
      }
    }


    // 会场
    if (props.schemeCacheInfo.places && props.schemeCacheInfo.places.length > 0) {

      let placesPlan = 0;
      let placesBill = 0;

      props.schemeCacheInfo.places.forEach((e) => {
        if (!e.billUnitPlacePrice) return;

        placesBill += e.billUnitPlacePrice || 0;
        placesPlan += e.schemeUnitPlacePrice || 0;

        if (e.hasLed) {
          if (!e.billUnitLedPrice) return;
          // 单价*LED数量
          placesBill += e.billUnitLedPrice * e.billLedNum;
          placesPlan += e.schemeUnitLedPrice * e.schemeLedNum;
        }
        if (e.hasTea) {
          if (!e.billUnitTeaPrice) return;
          // 茶歇单价*会场人数
          placesBill += e.schemeUnitTeaPrice * e.schemePersonNum;
          placesPlan += e.billUnitTeaPrice * e.billPersonNum;
        }
      });
      if (placesBill && placesBill > placesPlan * 1.1) {
        Modal.warning({
          title: '会场超过对应方案的10%！',
          okText: '确定',
        });
        return false;
      }
    }

    // 用餐
    if (props.schemeCacheInfo.caterings && props.schemeCacheInfo.caterings.length > 0) {

      let cateringsPlan = 0;
      let cateringsBill = 0;


      props.schemeCacheInfo.caterings.forEach((e) => {
        if (!e.billUnitPrice) return;

        cateringsBill += e.billPersonNum * e.billUnitPrice;
        cateringsPlan += e.schemePersonNum * e.schemeUnitPrice;
      });
      if (cateringsBill && cateringsBill > cateringsPlan * 1.1) {
        Modal.warning({
          title: '用餐超过对应方案的10%！',
          okText: '确定',
        });
        return false;
      }

    }

    // 用车
    if (props.schemeCacheInfo.vehicles && props.schemeCacheInfo.vehicles.length > 0) {
      let vehiclesPlan = 0;
      let vehiclesBill = 0;

      props.schemeCacheInfo.vehicles.forEach((e) => {
        if (!e.billVehicleNum) return;

        vehiclesPlan += e.schemeUnitPrice * e.schemeVehicleNum;
        vehiclesBill += e.billUnitPrice * e.billVehicleNum;
      });
      if (vehiclesBill && vehiclesBill > vehiclesPlan * 1.1) {
        Modal.warning({
          title: '用车超过对应方案的10%！',
          okText: '确定',
        });
        return false;
      }
    }

    // 服务人员
    if (props.schemeCacheInfo.attendants && props.schemeCacheInfo.attendants.length > 0) {
      let attendantsPlan = 0;
      let attendantsBill = 0;

      props.schemeCacheInfo.attendants.forEach((e) => {
        if (!e.billUnitPrice) return;
        attendantsBill += e.billUnitPrice * e.billPersonNum;
        attendantsPlan += e.schemeUnitPrice * e.schemePersonNum;
      });
      if (attendantsBill && attendantsBill > attendantsPlan * 1.1) {
        Modal.warning({
          title: '服务人员超过对应方案的10%！',
          okText: '确定',
        });
        return false;
      }
    }

    // 方案拓展
    if (props.schemeCacheInfo.activities && props.schemeCacheInfo.activities.length > 0) {
      let activitiesPlan = 0;
      let activitiesBill = 0;


      props.schemeCacheInfo.activities.forEach((e) => {
        if (!e.billUnitPrice) return;
        activitiesPlan += e.schemePersonNum * e.schemeUnitPrice;
        activitiesBill += e.billPersonNum * e.billUnitPrice;
      });
      if (activitiesBill && activitiesBill > activitiesPlan * 1.1) {
        Modal.warning({
          title: '扩展方案超过对应方案的10%！',
          okText: '确定',
        });
        return false;
      }

    }

    // 保险
    if (props.schemeCacheInfo.insurances && props.schemeCacheInfo.insurances.length > 0) {

      let insurancesPlan = 0;
      let insurancesBill = 0;

      props.schemeCacheInfo.insurances.forEach((e) => {

        insurancesPlan += e.schemePersonNum * e.schemeUnitPrice;
        insurancesBill += e.billPersonNum * e.billUnitPrice;
      });
      if (insurancesBill && insurancesBill > insurancesPlan * 1.1) {
        Modal.warning({
          title: '保险超过对应方案的10%！',
          okText: '确定',
        });
        return false;
      }

    }

    // 布展物料
    if (props.schemeCacheInfo.material && Object.keys(props.schemeCacheInfo.material).length > 0) {
      let materialPlan = 0;
      let materialBill = 0;

      props.schemeCacheInfo.material.materialDetails.forEach((e, idx) => {
        materialPlan += e.schemeUnitPrice * e.schemeMaterialNum;
        materialBill += e.billUnitPrice * e.billMaterialNum;
      });
      if (materialBill && materialBill > materialPlan * 1.1) {
        Modal.warning({
          title: '布展物料超过对应方案的10%！',
          okText: '确定',
        });
        return false;
      }
    }

    // 礼品
    if (props.schemeCacheInfo.presents && props.schemeCacheInfo.presents.length > 0) {

      let presentsPlan = 0;
      let presentsBill = 0;

      props.schemeCacheInfo.presents.forEach((e, idx) => {
        presentsPlan += e.schemeTotalPrice;
        presentsBill += e.billTotalPrice;
      });
      if (presentsBill && presentsBill > presentsPlan * 1.1) {
        Modal.warning({
          title: '礼品超过对应方案的10%！',
          okText: '确定',
        });
        return false;
      }
    }

    // 其他
    if (props.schemeCacheInfo.others && props.schemeCacheInfo.others.length > 0) {
      let othersPlan = 0;
      let othersBill = 0;

      props.schemeCacheInfo.others.forEach((e, idx) => {
        othersPlan += e.schemeTotalPrice;
        othersBill += e.billTotalPrice;
      });
      if (othersBill && othersBill > othersPlan * 1.1) {
        Modal.warning({
          title: '其他超过对应方案的10%！',
          okText: '确定',
        });
        return false;
      }
    }

    // 服务费
    if (
      props.schemeCacheInfo.serviceFee &&
      props.schemeCacheInfo.serviceFee.serviceFeeRate !== null &&
      props.schemeCacheInfo.serviceFee.schemeServiceFeeReal !== null
    ) {
      let demandTd3 = props.schemeCacheInfo.serviceFee.billServiceFeeReal;
      
    }

    if (props.leftPlanTotalPrice) {
      if (AllPrice.value && props.leftPlanTotalPrice < AllPrice.value) {
        Modal.warning({
          title: '账单总金额超过方案总金额，请重新规划',
          okText: '确定',
        });
        return false;
      }
    }

  }
  return true
}

// 暴露给父组件的方法
defineExpose({
  Totalamountverification
});

const schemePlanLabelList = ['需求', '方案', '合计（元）'];

onMounted(async () => { });
</script>

<template>
  <!-- 方案合计 -->
  <div class="scheme_total">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>合计</span>
    </div>

    <div class="total_table mt16">
      <div class="scheme_total_list1">
        <div class="scheme_demand_label" v-for="(label, index) in schemePlanLabelList" :key="index">
          {{ label }}
          <div class="label_shu" v-show="index < 2"></div>
        </div>
      </div>
      <div class="scheme_total_list1 scheme_total_list2" v-for="(item, idx) in newSchemeList" :key="idx">
        <div class="scheme_demand_label">
          <a-tooltip placement="topLeft">
            <template #title>
              {{ item.demandTd1 || '-' }}
            </template>
            {{ item.demandTd1 || '-' }}
          </a-tooltip>
        </div>
        <div class="scheme_demand_label">
          <a-tooltip placement="topLeft">
            <template #title>
              {{ item.demandTd2 || '-' }}
            </template>
            {{ item.demandTd2 || '-' }}
          </a-tooltip>
        </div>
        <div class="scheme_demand_label">
          <a-tooltip placement="topLeft">
            <template #title>
              {{ '¥' + item.demandTd3 || '-' }}
            </template>
            {{ '¥' + item.demandTd3 || '-' }}
          </a-tooltip>
        </div>
      </div>
    </div>

    <div class="scheme_total_btn mt16">
      <span class="total_text">合计：</span>
      <span class="total_num">{{
        '¥' +
        formatNumberThousands(
          AllPrice
        )
      }}</span>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_total {
  .interact_title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 500;

    .interact_shu {
      width: 4px;
      height: 20px;
      background: #1868db;
      border-radius: 2px;
    }

    span {
      font-size: 18px;
      font-weight: 500;
      color: #1d2129;
    }
  }

  .total_table {

    .scheme_total_list1,
    .scheme_total_list2 {
      width: 50%;
      display: flex;

      height: 36px;
      line-height: 36px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #1d2129;
      border-bottom: 1px solid #e5e6eb;
      background: #f7f8fa;

      .scheme_demand_label {
        padding: 0 16px;
        width: 100px;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        position: relative;

        &:nth-child(2) {
          width: calc(100% - 160px - 100px);
        }

        &:nth-child(3) {
          width: 160px;
          text-align: right;
        }

        .label_shu {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);

          width: 1px;
          height: 16px;
          background: #e5e6eb;
        }
      }
    }

    .scheme_total_list2 {
      background: #ffffff;
      font-weight: 400;
      color: #1d2129;
    }
  }

  .scheme_total_btn {
    width: 50%;

    padding: 0 14px;
    height: 44px;
    line-height: 44px;

    text-align: right;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;

    color: #f7f8fc;
    background: #1868db;
    border-radius: 4px;

    .total_text {
      font-size: 14px;
    }

    .total_num {
      font-size: 20px;
    }
  }
}
</style>
